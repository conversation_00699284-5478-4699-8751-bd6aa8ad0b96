'use client';

import { Button } from './ui/button';
import { Progress } from './ui/progress';
import { useUser } from '@/contexts/user-context';
import { Skeleton } from './ui/skeleton';

export function NavCreditsUsage() {
  const { dbUser, loading, error } = useUser();

  if (loading) {
    return (
      <div>
        <Skeleton className="h-4 bg-gray-200 mb-2 w-2/3" />
        <Skeleton className="h-2 bg-gray-200 mb-3" />
        <Skeleton className="h-8 bg-gray-200" />
      </div>
    );
  }

  if (error || !dbUser) {
    return (
      <div>
        <p className="text-sm text-gray-500">Credits: Unavailable</p>
        <Button size="sm" className="w-full mt-3" disabled>
          Upgrade Plan
        </Button>
      </div>
    );
  }

  const creditsUsed = dbUser.monthlyCreditsUsed;
  const creditsTotal = dbUser.monthlyCreditsLimit;
  const creditsPercentage =
    creditsTotal > 0 ? (creditsUsed / creditsTotal) * 100 : 0;

  return (
    <div>
      <p className="text-sm text-gray-600">
        Credits Used: {creditsUsed} / {creditsTotal}
      </p>
      <Progress value={creditsPercentage} className="mt-2" />
      <Button size="sm" className="w-full mt-3">
        Upgrade Plan
      </Button>
    </div>
  );
}
