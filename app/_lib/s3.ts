import { S3Client, PutObjectCommand, S3ClientConfig } from '@aws-sdk/client-s3';
import { getSignedUrl } from '@aws-sdk/s3-request-presigner';

const isLocal = process.env.AWS_S3_LOCAL === 'true';

// Initialize S3 client
const s3ClientOptions: S3ClientConfig = {
  region: process.env.AWS_REGION || 'us-east-1',
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID!,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY!,
  },
};

if (isLocal) {
  s3ClientOptions.endpoint =
    process.env.AWS_S3_LOCAL_ENDPOINT || 'http://localhost:4566';
  s3ClientOptions.forcePathStyle = true;
}

const s3Client = new S3Client(s3ClientOptions);

const BUCKET_NAME = process.env.AWS_S3_BUCKET!;

/**
 * Generate a presigned URL for uploading a file to S3
 */
export async function generatePresignedUploadUrl(
  key: string,
  contentType: string,
  expiresIn: number = 3600 // 1 hour default
): Promise<string> {
  const command = new PutObjectCommand({
    Bucket: BUCKET_NAME,
    Key: key,
    ContentType: contentType,
  });

  const presignedUrl = await getSignedUrl(s3Client, command, {
    expiresIn,
  });

  return presignedUrl;
}

/**
 * Generate a unique file key for S3 storage
 */
export function generateFileKey(
  userId: string,
  originalFilename: string
): string {
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = originalFilename.split('.').pop();

  return `${userId}/${timestamp}-${randomString}.${extension}`;
}

/**
 * Get the public URL for a file in S3
 */
export function getFileUrl(key: string): string {
  const isLocal = process.env.AWS_S3_LOCAL === 'true';
  if (isLocal) {
    // LocalStack endpoint (default: http://localhost:4566)
    const endpoint =
      process.env.AWS_S3_LOCAL_ENDPOINT || 'http://localhost:4566';
    return `${endpoint}/${BUCKET_NAME}/${key}`;
  }
  return `https://${BUCKET_NAME}.s3.${
    process.env.AWS_REGION || 'us-east-1'
  }.amazonaws.com/${key}`;
}

/**
 * Validate file type and size
 */
export function validateFile(file: { type: string; size: number }) {
  const allowedTypes = [
    'application/pdf',
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp',
  ];

  const maxSize = 10 * 1024 * 1024; // 10MB

  if (!allowedTypes.includes(file.type)) {
    throw new Error(
      'File type not allowed. Please upload PDF, JPEG, PNG, GIF, or WebP files.'
    );
  }

  if (file.size > maxSize) {
    throw new Error('File size too large. Maximum size is 10MB.');
  }

  return true;
}
