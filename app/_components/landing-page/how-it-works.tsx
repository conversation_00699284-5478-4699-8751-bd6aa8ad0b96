import { UploadCloud, Wand2, DownloadCloud } from 'lucide-react';

export default function HowItWorks() {
    return (
        <section className="py-20">
            <div className="container mx-auto px-6">
                <div className="text-center mb-16">
                    <h2 className="text-3xl md:text-4xl font-bold">
                        Get Structured Data in 3 Simple Steps
                    </h2>
                </div>
                <div className="grid md:grid-cols-3 gap-8 text-center relative">
                    {/* Step 1 */}
                    <div className="flex flex-col items-center">
                        <div className="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg">
                            1
                        </div>
                        <UploadCloud className="w-20 h-20 text-indigo-500 my-6" />
                        <h3 className="text-xl font-semibold">
                            Upload Document
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Drag & drop a PDF, JPG, or PNG file. Or send it via
                            our API.
                        </p>
                    </div>
                    {/* Step 2 */}
                    <div className="flex flex-col items-center">
                        <div className="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg">
                            2
                        </div>
                        <Wand2 className="w-20 h-20 text-indigo-500 my-6" />
                        <h3 className="text-xl font-semibold">
                            Let AI Work Its Magic
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Unfurl&apos;s AI reads, understands, and structures
                            your data in seconds.
                        </p>
                    </div>
                    {/* Step 3 */}
                    <div className="flex flex-col items-center">
                        <div className="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg">
                            3
                        </div>
                        <DownloadCloud className="w-20 h-20 text-indigo-500 my-6" />
                        <h3 className="text-xl font-semibold">
                            Export & Integrate
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Download as CSV/JSON or use the API to send data to
                            your other apps.
                        </p>
                    </div>
                    {/* Connecting line for desktop */}
                    <div className="hidden md:block absolute top-8 left-0 w-full h-0.5 bg-gray-200 -z-10"></div>
                </div>
            </div>
        </section>
    );
}
