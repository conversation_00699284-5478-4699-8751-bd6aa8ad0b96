# Database Setup Guide

This guide will help you set up the PostgreSQL database for the Unfurl application.

## Prerequisites

- Node.js 18+ installed
- PostgreSQL 14+ installed locally OR access to a cloud PostgreSQL instance

## Option 1: Local PostgreSQL Setup

### 1. Install PostgreSQL

**macOS (using Homebrew):**
```bash
brew install postgresql
brew services start postgresql
```

**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install postgresql postgresql-contrib
sudo systemctl start postgresql
sudo systemctl enable postgresql
```

**Windows:**
Download and install from [postgresql.org](https://www.postgresql.org/download/windows/)

### 2. Create Database and User

```bash
# Connect to PostgreSQL as superuser
sudo -u postgres psql

# Create database and user
CREATE DATABASE unfurl_dev;
CREATE USER unfurl_user WITH PASSWORD 'your_secure_password';
GRANT ALL PRIVILEGES ON DATABASE unfurl_dev TO unfurl_user;
\q
```

### 3. Update Environment Variables

Update your `.env` file:
```env
DATABASE_URL="postgresql://unfurl_user:your_secure_password@localhost:5432/unfurl_dev?schema=public"
```

## Option 2: Cloud Database (Recommended for Production)

### Supabase (Free tier available)
1. Go to [supabase.com](https://supabase.com)
2. Create a new project
3. Get your database URL from Settings > Database
4. Update your `.env` file with the connection string

### Railway (Free tier available)
1. Go to [railway.app](https://railway.app)
2. Create a new PostgreSQL database
3. Copy the connection string
4. Update your `.env` file

### Neon (Free tier available)
1. Go to [neon.tech](https://neon.tech)
2. Create a new database
3. Copy the connection string
4. Update your `.env` file

## Database Migration and Setup

Once you have your database configured:

### 1. Generate Prisma Client
```bash
npm run db:generate
```

### 2. Apply Database Schema

**For Development (using db push):**
```bash
npm run db:push
```

**For Production (using migrations):**
```bash
npm run db:migrate
```

### 3. Seed the Database (Optional)
```bash
npm run db:seed
```

### 4. Verify Setup
```bash
npm run db:studio
```

This will open Prisma Studio in your browser where you can view and edit your database.

## Environment Variables

Make sure your `.env` file contains all necessary variables:

```env
# Database
DATABASE_URL="postgresql://username:password@localhost:5432/unfurl_dev?schema=public"

# Clerk Authentication
NEXT_PUBLIC_CLERK_PUBLISHABLE_KEY="your-clerk-publishable-key"
CLERK_SECRET_KEY="your-clerk-secret-key"
NEXT_PUBLIC_CLERK_SIGN_IN_URL="/sign-in"
NEXT_PUBLIC_CLERK_SIGN_UP_URL="/sign-up"
NEXT_PUBLIC_CLERK_AFTER_SIGN_IN_URL="/dashboard"
NEXT_PUBLIC_CLERK_AFTER_SIGN_UP_URL="/dashboard"

# File Storage
AWS_ACCESS_KEY_ID="your-aws-access-key"
AWS_SECRET_ACCESS_KEY="your-aws-secret-key"
AWS_REGION="us-east-1"
AWS_S3_BUCKET="unfurl-documents"

# AI Services
OPENAI_API_KEY="your-openai-api-key"

# Stripe
STRIPE_SECRET_KEY="your-stripe-secret-key"
STRIPE_PUBLISHABLE_KEY="your-stripe-publishable-key"

# Redis/Queue
REDIS_URL="redis://localhost:6379"
```

## Troubleshooting

### Connection Issues
- Verify PostgreSQL is running: `pg_isready`
- Check your connection string format
- Ensure the database exists and user has proper permissions

### Migration Issues
- If migrations fail, try `npm run db:push` for development
- For production, ensure you have backup before running migrations

### Permission Issues
- Make sure your database user has CREATE, ALTER, DROP permissions
- For cloud databases, check firewall settings

## Production Considerations

1. **Use Migrations**: Always use `npm run db:migrate` in production
2. **Backup Strategy**: Set up regular database backups
3. **Connection Pooling**: Consider using connection pooling for high traffic
4. **Monitoring**: Set up database monitoring and alerts
5. **Security**: Use strong passwords and enable SSL connections

## Next Steps

After setting up the database:
1. Run the development server: `npm run dev`
2. Implement authentication (Task S1-03)
3. Create the core UI layout (Task S1-04)

## Database Schema Overview

The database includes:
- **User management** with Clerk authentication compatibility
- **Credit-based billing** with flat rate pricing (1 credit = 1 page)
- **Document storage** with metadata and credit tracking
- **Job processing** for async AI operations
- **Template system** for data extraction
- **API key management** for developer access
- **Credit transaction history** for complete audit trail

## Credit System Features

- **Simple Pricing**: 1 credit = 1 page (includes OCR, AI extraction, and processing)
- **Free Plan**: 10 credits per month
- **Paid Plans**: 500, 2000, or 10000+ credits per month
- **Credit Rollover**: Unused credits roll over (up to 2x monthly limit)
- **Real-time Tracking**: Monitor credit balance and usage in real-time

For detailed schema information, see `prisma/README.md`.
