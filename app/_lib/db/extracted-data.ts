import { prisma } from '../prisma';

// Extracted Data operations
export const extractedDataOperations = {
  async create(input: {
    data: Record<string, unknown>;
    confidence?: number;
    userId: string;
    documentId: string;
    jobId: string;
  }) {
    return prisma.extractedData.create({
      data: {
        data: input.data as any, // Prisma expects JsonValue
        confidence: input.confidence,
        userId: input.userId,
        documentId: input.documentId,
        jobId: input.jobId,
      },
    });
  },

  async validate(id: string, validatedBy: string) {
    return prisma.extractedData.update({
      where: { id },
      data: {
        isValidated: true,
        validatedAt: new Date(),
        validatedBy,
      },
    });
  },

  async findByDocument(documentId: string) {
    return prisma.extractedData.findMany({
      where: { documentId },
      orderBy: { createdAt: 'desc' },
    });
  },
};
