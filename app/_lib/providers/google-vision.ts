// Google Vision API provider implementation
import type { AIResult } from '../ai';

const GOOGLE_VISION_API_KEY = process.env.GOOGLE_VISION_API_KEY;
const GOOGLE_VISION_API_URL =
  'https://vision.googleapis.com/v1/images:annotate';

export async function callGoogleVisionAPI(
  documentUrl: string
): Promise<AIResult> {
  if (!GOOGLE_VISION_API_KEY) {
    throw new Error(
      'GOOGLE_VISION_API_KEY is not set in environment variables'
    );
  }

  // Prepare the request body for Google Vision API
  const body = {
    requests: [
      {
        image: { source: { imageUri: documentUrl } },
        features: [{ type: 'DOCUMENT_TEXT_DETECTION' }],
      },
    ],
  };

  const response = await fetch(
    `${GOOGLE_VISION_API_URL}?key=${GOOGLE_VISION_API_KEY}`,
    {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(body),
    }
  );

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`Google Vision API error: ${error}`);
  }

  const data = await response.json();
  const annotation = data.responses?.[0]?.fullTextAnnotation;
  const text = annotation?.text || '';
  // Google Vision does not return page count directly, but provides pages in the annotation
  const pages = annotation?.pages?.length || 1;

  return {
    pages,
    data: { text, annotation },
  };
}
