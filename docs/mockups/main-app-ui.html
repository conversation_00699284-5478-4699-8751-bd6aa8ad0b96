<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Unfurl Application</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <script src="https://unpkg.com/lucide@latest"></script>
        <style>
            body {
                font-family: 'Inter', sans-serif;
                overflow: hidden; /* Prevent body scroll */
            }
            .sidebar-icon {
                stroke-width: 1.5;
            }
            /* Custom scrollbar for webkit browsers */
            ::-webkit-scrollbar {
                width: 8px;
                height: 8px;
            }
            ::-webkit-scrollbar-track {
                background: #f1f5f9;
            }
            ::-webkit-scrollbar-thumb {
                background: #cbd5e1;
                border-radius: 10px;
            }
            ::-webkit-scrollbar-thumb:hover {
                background: #94a3b8;
            }
            /* For smooth transitions */
            .transition-all {
                transition: all 0.3s ease-in-out;
            }
            /* Highlight box for document preview */
            .highlight-box {
                position: absolute;
                background-color: rgba(79, 70, 229, 0.2);
                border: 2px solid #4f46e5;
                border-radius: 4px;
                z-index: 10;
                display: none; /* Hidden by default */
            }
        </style>
    </head>
    <body class="bg-gray-100">
        <div class="flex h-screen bg-gray-100">
            <!-- Sidebar -->
            <aside
                id="sidebar"
                class="w-64 bg-white flex flex-col border-r border-gray-200 transition-all fixed inset-y-0 left-0 z-30 md:relative md:translate-x-0 -translate-x-full"
            >
                <!-- Logo and Collapse Button -->
                <div
                    class="flex items-center justify-between px-4 h-16 border-b border-gray-200"
                >
                    <a
                        href="#"
                        class="flex items-center space-x-2 text-xl font-bold text-gray-900"
                    >
                        <svg
                            width="28"
                            height="28"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                            class="text-indigo-600"
                        >
                            <path
                                d="M4 22V8.5C4 7.22876 4 6.59313 4.25301 6.0754C4.47467 5.62137 4.82137 5.27467 5.2754 5.05301C5.79313 4.8 6.42876 4.8 7.7 4.8H12M4 22L8 18M4 22L1 19"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                            <path
                                d="M20 2H10.5C9.22876 2 8.59313 2 8.0754 2.25301C7.62137 2.47467 7.27467 2.82137 7.05301 3.2754C6.8 3.79313 6.8 4.42876 6.8 5.7V15.5C6.8 16.7712 6.8 17.4069 7.05301 17.9246C7.27467 18.3786 7.62137 18.7253 8.0754 18.947C8.59313 19.2 9.22876 19.2 10.5 19.2H14.5M20 2L16 6M20 2L23 5"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                        <span>Unfurl</span>
                    </a>
                </div>

                <nav class="flex-1 px-4 py-4 space-y-2">
                    <a
                        href="#"
                        class="flex items-center px-4 py-2.5 text-gray-700 bg-indigo-50 rounded-lg"
                    >
                        <i
                            data-lucide="layout-dashboard"
                            class="sidebar-icon w-6 h-6"
                        ></i>
                        <span class="ml-4 font-medium">Dashboard</span>
                    </a>
                    <a
                        href="#"
                        class="flex items-center px-4 py-2.5 text-gray-500 hover:bg-gray-100 rounded-lg"
                    >
                        <i
                            data-lucide="file-text"
                            class="sidebar-icon w-6 h-6"
                        ></i>
                        <span class="ml-4 font-medium">Documents</span>
                    </a>
                    <a
                        href="#"
                        class="flex items-center px-4 py-2.5 text-gray-500 hover:bg-gray-100 rounded-lg"
                    >
                        <i
                            data-lucide="puzzle"
                            class="sidebar-icon w-6 h-6"
                        ></i>
                        <span class="ml-4 font-medium">Templates</span>
                    </a>
                    <a
                        href="#"
                        class="flex items-center px-4 py-2.5 text-gray-500 hover:bg-gray-100 rounded-lg"
                    >
                        <i
                            data-lucide="code-2"
                            class="sidebar-icon w-6 h-6"
                        ></i>
                        <span class="ml-4 font-medium">API</span>
                    </a>
                </nav>

                <div class="px-4 py-4 border-t border-gray-200">
                    <div class="p-4 bg-gray-50 rounded-lg">
                        <p class="text-sm text-gray-600">
                            Credits Used: 42 / 50
                        </p>
                        <div class="w-full bg-gray-200 rounded-full h-1.5 mt-2">
                            <div
                                class="bg-indigo-600 h-1.5 rounded-full"
                                style="width: 84%"
                            ></div>
                        </div>
                        <button
                            class="w-full mt-3 bg-indigo-100 text-indigo-700 text-sm font-semibold py-1.5 rounded-md hover:bg-indigo-200"
                        >
                            Upgrade Plan
                        </button>
                    </div>
                </div>
            </aside>

            <!-- Main Content -->
            <div class="flex-1 flex flex-col w-full">
                <!-- Top Bar -->
                <header
                    class="flex items-center justify-between h-16 px-6 bg-white border-b border-gray-200 sticky top-0 z-20"
                >
                    <button
                        id="sidebar-toggle"
                        class="md:hidden p-2 rounded-md text-gray-500 hover:bg-gray-100"
                    >
                        <i data-lucide="menu" class="w-6 h-6"></i>
                    </button>
                    <h1
                        class="text-2xl font-semibold text-gray-800 hidden md:block"
                    >
                        Documents
                    </h1>
                    <div class="flex items-center space-x-4">
                        <button class="p-2 rounded-full hover:bg-gray-100">
                            <i
                                data-lucide="bell"
                                class="w-6 h-6 text-gray-600"
                            ></i>
                        </button>
                        <div class="flex items-center">
                            <img
                                src="https://i.pravatar.cc/40?u=a042581f4e29026704d"
                                alt="User avatar"
                                class="w-10 h-10 rounded-full"
                            />
                        </div>
                    </div>
                </header>

                <!-- App Layout -->
                <div class="flex-1 flex flex-col md:flex-row overflow-hidden">
                    <!-- Document List Panel -->
                    <div
                        class="w-full md:w-1/3 lg:w-1/4 xl:w-1/5 bg-white border-r border-gray-200 flex flex-col"
                    >
                        <div class="p-4 border-b">
                            <button
                                id="upload-btn"
                                class="w-full bg-indigo-600 text-white font-bold py-3 px-4 rounded-lg shadow hover:bg-indigo-700 flex items-center justify-center space-x-2"
                            >
                                <i
                                    data-lucide="upload-cloud"
                                    class="w-5 h-5"
                                ></i>
                                <span>Upload Document</span>
                            </button>
                        </div>
                        <div
                            id="document-list"
                            class="flex-1 overflow-y-auto p-2 space-y-1"
                        >
                            <!-- Documents will be dynamically rendered here, but we'll keep one for template -->
                            <a
                                href="#"
                                class="document-item block p-3 bg-indigo-50 border-l-4 border-indigo-500 rounded-r-md cursor-pointer"
                                data-doc-id="1"
                            >
                                <p
                                    class="font-semibold text-sm text-gray-800 truncate"
                                >
                                    Invoice_INV-2025-06-11.pdf
                                </p>
                                <div
                                    class="flex items-center justify-between text-xs mt-1"
                                >
                                    <span class="text-yellow-600 font-medium">
                                        Needs Review
                                    </span>
                                    <span class="text-gray-500">Today</span>
                                </div>
                            </a>
                            <a
                                href="#"
                                class="document-item block p-3 hover:bg-gray-50 rounded-md cursor-pointer"
                                data-doc-id="2"
                            >
                                <p
                                    class="font-semibold text-sm text-gray-800 truncate"
                                >
                                    Receipt_TotalGas_0610.jpg
                                </p>
                                <div
                                    class="flex items-center justify-between text-xs mt-1"
                                >
                                    <span class="text-green-600 font-medium">
                                        Completed
                                    </span>
                                    <span class="text-gray-500">Yesterday</span>
                                </div>
                            </a>
                        </div>
                    </div>

                    <!-- Document Viewer -->
                    <div
                        class="flex-1 bg-gray-100 flex flex-col items-center justify-center p-4 relative"
                    >
                        <div
                            class="w-full h-full bg-white rounded-lg shadow-inner border border-gray-200 overflow-auto relative"
                        >
                            <img
                                src="https://i.imgur.com/7T20soH.png"
                                alt="Document Preview"
                                class="w-full h-auto"
                                onerror="this.onerror=null;this.src='https://placehold.co/800x1131/FFFFFF/000000?text=Invoice+Image';"
                            />
                            <!-- Highlight Boxes -->
                            <div
                                class="highlight-box"
                                data-field="invoice_number"
                                style="
                                    top: 13.5%;
                                    left: 68%;
                                    width: 22%;
                                    height: 3%;
                                "
                            ></div>
                            <div
                                class="highlight-box"
                                data-field="vendor_name"
                                style="
                                    top: 25.5%;
                                    left: 10%;
                                    width: 25%;
                                    height: 3%;
                                "
                            ></div>
                            <div
                                class="highlight-box"
                                data-field="invoice_date"
                                style="
                                    top: 16%;
                                    left: 68%;
                                    width: 22%;
                                    height: 3%;
                                "
                            ></div>
                            <div
                                class="highlight-box"
                                data-field="total_amount"
                                style="
                                    top: 75%;
                                    left: 75%;
                                    width: 15%;
                                    height: 4%;
                                "
                            ></div>
                            <div
                                class="highlight-box"
                                data-field="due_date"
                                style="
                                    top: 18.5%;
                                    left: 68%;
                                    width: 22%;
                                    height: 3%;
                                "
                            ></div>
                        </div>
                    </div>

                    <!-- Extraction Results Panel -->
                    <div
                        class="w-full md:w-1/3 lg:w-1/4 xl:w-1/4 bg-white border-l border-gray-200 flex flex-col"
                    >
                        <div
                            class="p-4 border-b flex justify-between items-center"
                        >
                            <h2 class="text-lg font-semibold">
                                Extracted Data
                            </h2>
                        </div>
                        <div
                            id="data-form"
                            class="flex-1 overflow-y-auto p-4 space-y-4"
                        >
                            <div class="form-field">
                                <label
                                    class="text-sm font-medium text-gray-600 flex items-center"
                                >
                                    Invoice Number
                                </label>
                                <input
                                    type="text"
                                    value="INV-12345"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    data-field="invoice_number"
                                />
                            </div>
                            <div class="form-field">
                                <label
                                    class="text-sm font-medium text-gray-600 flex items-center"
                                >
                                    Vendor Name
                                </label>
                                <input
                                    type="text"
                                    value="Stark Industries"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    data-field="vendor_name"
                                />
                            </div>
                            <div class="form-field">
                                <label
                                    class="text-sm font-medium text-gray-600 flex items-center"
                                >
                                    Invoice Date
                                </label>
                                <input
                                    type="date"
                                    value="2025-06-12"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    data-field="invoice_date"
                                />
                            </div>
                            <div class="form-field">
                                <label
                                    class="text-sm font-medium text-gray-600 flex items-center"
                                >
                                    Total Amount
                                </label>
                                <input
                                    type="text"
                                    value="$9,876.54"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    data-field="total_amount"
                                />
                            </div>
                            <div class="form-field">
                                <label
                                    class="text-sm font-medium text-gray-600 flex items-center"
                                >
                                    Due Date
                                </label>
                                <input
                                    type="date"
                                    value="2025-07-12"
                                    class="mt-1 w-full p-2 border border-gray-300 rounded-md"
                                    data-field="due_date"
                                />
                            </div>
                        </div>
                        <div class="p-4 border-t bg-white">
                            <button
                                class="w-full bg-green-600 text-white font-bold py-3 px-4 rounded-lg shadow hover:bg-green-700"
                            >
                                Approve & Finalize
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Upload Modal -->
        <div
            id="upload-modal"
            class="fixed inset-0 bg-gray-900 bg-opacity-50 flex items-center justify-center z-50 hidden"
        >
            <div class="bg-white rounded-lg shadow-xl p-8 w-full max-w-lg mx-4">
                <div class="flex justify-between items-center mb-4">
                    <h2 class="text-2xl font-bold">Upload Document</h2>
                    <button
                        id="close-modal-btn"
                        class="p-2 rounded-full hover:bg-gray-200"
                    >
                        <i data-lucide="x" class="w-6 h-6 text-gray-600"></i>
                    </button>
                </div>
                <div
                    id="drop-zone"
                    class="border-2 border-dashed border-gray-300 rounded-lg p-10 text-center cursor-pointer hover:border-indigo-500 hover:bg-indigo-50"
                >
                    <i
                        data-lucide="upload-cloud"
                        class="w-16 h-16 mx-auto text-gray-400"
                    ></i>
                    <p class="mt-4 text-lg font-semibold text-gray-700">
                        Drag & drop files here
                    </p>
                    <p class="text-gray-500">or</p>
                    <button
                        id="browse-files-btn"
                        class="mt-2 text-indigo-600 font-semibold hover:underline"
                    >
                        Browse files
                    </button>
                    <input
                        type="file"
                        id="file-input"
                        class="hidden"
                        multiple
                    />
                </div>
                <div class="mt-6 text-right">
                    <button
                        id="cancel-upload-btn"
                        class="text-gray-600 font-medium py-2 px-4 rounded-lg hover:bg-gray-100 mr-2"
                    >
                        Cancel
                    </button>
                    <button
                        class="bg-indigo-600 text-white font-bold py-2 px-4 rounded-lg shadow hover:bg-indigo-700"
                    >
                        Upload
                    </button>
                </div>
            </div>
        </div>

        <script>
            lucide.createIcons();

            document.addEventListener('DOMContentLoaded', () => {
                // --- Elements ---
                const sidebar = document.getElementById('sidebar');
                const sidebarToggle = document.getElementById('sidebar-toggle');
                const documentList = document.getElementById('document-list');
                const dataForm = document.getElementById('data-form');
                const highlightBoxes =
                    document.querySelectorAll('.highlight-box');

                // Modal elements
                const uploadModal = document.getElementById('upload-modal');
                const uploadBtn = document.getElementById('upload-btn');
                const closeModalBtn =
                    document.getElementById('close-modal-btn');
                const cancelUploadBtn =
                    document.getElementById('cancel-upload-btn');
                const dropZone = document.getElementById('drop-zone');

                // --- Sidebar Toggle ---
                sidebarToggle.addEventListener('click', () => {
                    sidebar.classList.toggle('-translate-x-full');
                });

                // --- Document Selection ---
                documentList.addEventListener('click', (e) => {
                    const clickedItem = e.target.closest('.document-item');
                    if (!clickedItem) return;

                    // Remove active state from all items
                    document
                        .querySelectorAll('.document-item')
                        .forEach((item) => {
                            item.classList.remove(
                                'bg-indigo-50',
                                'border-l-4',
                                'border-indigo-500'
                            );
                            item.classList.add('hover:bg-gray-50');
                        });

                    // Add active state to clicked item
                    clickedItem.classList.add(
                        'bg-indigo-50',
                        'border-l-4',
                        'border-indigo-500'
                    );
                    clickedItem.classList.remove('hover:bg-gray-50');
                });

                // --- Interactive Form Fields & Highlights ---
                dataForm.addEventListener('focusin', (e) => {
                    const input = e.target.closest('input');
                    if (!input || !input.dataset.field) return;

                    const fieldName = input.dataset.field;
                    const highlightBox = document.querySelector(
                        `.highlight-box[data-field="${fieldName}"]`
                    );

                    if (highlightBox) {
                        highlightBox.style.display = 'block';
                    }
                });

                dataForm.addEventListener('focusout', (e) => {
                    const input = e.target.closest('input');
                    if (!input || !input.dataset.field) return;

                    const fieldName = input.dataset.field;
                    const highlightBox = document.querySelector(
                        `.highlight-box[data-field="${fieldName}"]`
                    );

                    if (highlightBox) {
                        highlightBox.style.display = 'none';
                    }
                });

                // --- Upload Modal Logic ---
                const showModal = () => uploadModal.classList.remove('hidden');
                const hideModal = () => uploadModal.classList.add('hidden');

                uploadBtn.addEventListener('click', showModal);
                closeModalBtn.addEventListener('click', hideModal);
                cancelUploadBtn.addEventListener('click', hideModal);
                uploadModal.addEventListener('click', (e) => {
                    if (e.target === uploadModal) {
                        // Click on overlay
                        hideModal();
                    }
                });

                // Drag and drop logic
                dropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    dropZone.classList.add('border-indigo-500', 'bg-indigo-50');
                });
                dropZone.addEventListener('dragleave', (e) => {
                    e.preventDefault();
                    dropZone.classList.remove(
                        'border-indigo-500',
                        'bg-indigo-50'
                    );
                });
                dropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    dropZone.classList.remove(
                        'border-indigo-500',
                        'bg-indigo-50'
                    );
                    const files = e.dataTransfer.files;
                    console.log('Files dropped:', files);
                    // Handle file upload here
                });
            });
        </script>
    </body>
</html>
