/**
 * Example usage of Mistral OCR with template-based data extraction
 * This file demonstrates how to use the Mistral OCR implementation
 * with the Template model for structured data extraction.
 */

import { callMistralOCRAPI } from '../providers/mistral-ocr';
import { templateOperations } from '../db';
import { prisma } from '../prisma';
import type { Template } from '@prisma/client';

/**
 * Example 1: Basic OCR without template
 */
export async function basicOCRExample() {
  const documentUrl = 'https://example.com/sample-invoice.pdf';

  try {
    const result = await callMistralOCRAPI(documentUrl);

    console.log('OCR Result:', {
      pages: result.pages,
      hasRawText: !!result.data.rawText,
      confidence: result.confidence,
    });

    return result;
  } catch (error) {
    console.error('Basic OCR failed:', error);
    throw error;
  }
}

/**
 * Example 2: Template-based extraction with invoice template
 */
export async function invoiceExtractionExample() {
  const documentUrl = 'https://example.com/sample-invoice.pdf';

  try {
    // Get the pre-built invoice template
    const templates = await templateOperations.findPublic();
    const invoiceTemplate = templates.find((t) => t.documentType === 'invoice');

    if (!invoiceTemplate) {
      throw new Error('Invoice template not found');
    }

    // Process document with template
    const result = await callMistralOCRAPI(documentUrl, {
      template: invoiceTemplate,
      model: 'pixtral-12b-2409',
    });

    console.log('Template-based extraction result:', {
      pages: result.pages,
      confidence: result.confidence,
      extractedFields: Object.keys(result.data).filter(
        (key) => key !== 'rawText'
      ),
      templateUsed: result.data.template,
    });

    return result;
  } catch (error) {
    console.error('Invoice extraction failed:', error);
    throw error;
  }
}

/**
 * Example 3: Custom template creation and usage
 */
export async function customTemplateExample(userId: string) {
  const documentUrl = 'https://example.com/custom-form.pdf';

  try {
    // Create a custom template for a specific form
    const customTemplate = await templateOperations.create({
      name: 'Employee Information Form',
      description: 'Template for extracting employee data from HR forms',
      documentType: 'employee_form',
      fields: {
        employee_id: { type: 'string', required: true },
        full_name: { type: 'string', required: true },
        email: { type: 'email', required: true },
        phone: { type: 'phone', required: false },
        department: { type: 'string', required: true },
        start_date: { type: 'date', required: true },
        salary: { type: 'number', required: false },
        emergency_contact: {
          type: 'object',
          required: false,
          properties: {
            name: { type: 'string' },
            phone: { type: 'phone' },
            relationship: { type: 'string' },
          },
        },
      },
      userId,
    });

    // Use the custom template for extraction
    const result = await callMistralOCRAPI(documentUrl, {
      template: customTemplate,
    });

    console.log('Custom template extraction result:', {
      templateId: customTemplate.id,
      templateName: customTemplate.name,
      extractedData: result.data,
      confidence: result.confidence,
    });

    return { template: customTemplate, result };
  } catch (error) {
    console.error('Custom template extraction failed:', error);
    throw error;
  }
}

/**
 * Example 4: Receipt processing with validation
 */
export async function receiptProcessingExample() {
  const documentUrl = 'https://example.com/receipt.jpg';

  try {
    // Get receipt template
    const templates = await templateOperations.findPublic();
    const receiptTemplate = templates.find((t) => t.documentType === 'receipt');

    if (!receiptTemplate) {
      throw new Error('Receipt template not found');
    }

    // Process receipt
    const result = await callMistralOCRAPI(documentUrl, {
      template: receiptTemplate,
    });

    // Validate extraction quality
    const validation = validateExtractionResult(result, receiptTemplate);

    console.log('Receipt processing result:', {
      ...validation,
      extractedData: result.data,
    });

    return { result, validation };
  } catch (error) {
    console.error('Receipt processing failed:', error);
    throw error;
  }
}

/**
 * Example 5: Business card extraction
 */
export async function businessCardExample() {
  const documentUrl = 'https://example.com/business-card.jpg';

  try {
    // Get business card template
    const templates = await templateOperations.findPublic();
    const businessCardTemplate = templates.find(
      (t) => t.documentType === 'business_card'
    );

    if (!businessCardTemplate) {
      throw new Error('Business card template not found');
    }

    const result = await callMistralOCRAPI(documentUrl, {
      template: businessCardTemplate,
    });

    console.log('Business card extraction:', {
      confidence: result.confidence,
      contactInfo: {
        name: result.data.name,
        company: result.data.company,
        email: result.data.email,
        phone: result.data.phone,
      },
    });

    return result;
  } catch (error) {
    console.error('Business card extraction failed:', error);
    throw error;
  }
}

/**
 * Helper function to validate extraction results
 */
function validateExtractionResult(result: any, template: Template) {
  const fields = template.fields as any;
  const requiredFields = Object.keys(fields).filter(
    (key) => fields[key].required
  );
  const extractedFields = Object.keys(result.data).filter(
    (key) => key !== 'rawText' && key !== 'template'
  );

  const missingRequired = requiredFields.filter(
    (field) => !result.data[field] || result.data[field] === null
  );

  const extractionRate = extractedFields.length / Object.keys(fields).length;
  const requiredFieldsRate =
    (requiredFields.length - missingRequired.length) / requiredFields.length;

  return {
    isValid: missingRequired.length === 0,
    confidence: result.confidence,
    extractionRate: Math.round(extractionRate * 100),
    requiredFieldsRate: Math.round(requiredFieldsRate * 100),
    missingRequired,
    extractedFields,
    totalFields: Object.keys(fields).length,
  };
}

/**
 * Example 6: Batch processing multiple documents
 */
export async function batchProcessingExample(
  documentUrls: string[],
  templateId: string
) {
  const results = [];

  try {
    // Get template
    const template = await prisma.template.findUnique({
      where: { id: templateId },
    });

    if (!template) {
      throw new Error(`Template ${templateId} not found`);
    }

    // Process each document
    for (const documentUrl of documentUrls) {
      try {
        const result = await callMistralOCRAPI(documentUrl, { template });
        const validation = validateExtractionResult(result, template);

        results.push({
          documentUrl,
          success: true,
          result,
          validation,
        });
      } catch (error) {
        results.push({
          documentUrl,
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        });
      }
    }

    // Summary
    const successful = results.filter((r) => r.success).length;
    const avgConfidence =
      results
        .filter((r) => r.success)
        .reduce((sum, r) => sum + (r.result?.confidence || 0), 0) / successful;

    console.log('Batch processing summary:', {
      total: documentUrls.length,
      successful,
      failed: documentUrls.length - successful,
      averageConfidence: Math.round(avgConfidence * 100) / 100,
    });

    return results;
  } catch (error) {
    console.error('Batch processing failed:', error);
    throw error;
  }
}
