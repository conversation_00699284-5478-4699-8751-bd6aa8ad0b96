# **AI Development Rules & Guidelines: Unfurl v2.0 (Next.js Stack)**

**Objective:** This document provides the complete set of rules, standards, and context required to develop the "Unfurl" application. As my AI development assistant, you MUST adhere to these rules at all times to ensure consistency, quality, and maintainability of the codebase.

### **Rule 1: Project Identity & Mission**

1.1. Product Name: Unfurl
1.2. Tagline: Intelligent data extraction, simplified.
1.3. Core Mission: Your primary goal is to build a SaaS application that automates data extraction from documents (PDFs, images) using AI. The user experience must be simple for non-technical users but powerful for developers via an API.

### **Rule 2: Architecture & Technology Stack (Next.js)**

2.1. Primary Framework: Next.js (App Router) with TypeScript. This is a full-stack application. Next.js will be used for the frontend UI, backend API routes, and all server-side logic.
2.2. Database & ORM:
\* Database: PostgreSQL.
\* ORM: Prisma is the exclusive ORM for all database interactions. You must use the Prisma Client for type-safe queries. The database schema is defined in prisma/schema.prisma.
2.3. Authentication: NextAuth.js is the standard for handling all user authentication, including email/password and social providers like Google.
2.4. Styling: Tailwind CSS is the exclusive styling solution.
2.5. Asynchronous Tasks: Long-running AI jobs will be managed by a serverless-friendly queueing service like Vercel KV (Redis) or Upstash.

### **Rule 3: API & Data Handling**

3.1. API Implementation: All backend API endpoints will be implemented as Next.js API Routes within the /app/api/ directory.
3.2. Data Format: All API responses MUST use JSON.
3.3. API Authentication: Public developer-facing API routes must be protected and require a Bearer token (API Key). Internal UI-facing API routes will be protected by NextAuth.js sessions.

### **Rule 4: Coding Conventions & Style**

4.1. Language: TypeScript. any should be used sparingly. Strive for strong type safety.
4.2. Style Guide: Strictly follow the Airbnb JavaScript Style Guide. Use a code formatter like Prettier to ensure consistency.
4.3. Naming Conventions:
\* Components: PascalCase (e.g., DocumentViewer.tsx).
\* Variables & Functions: camelCase (e.g., getUserById).
\* Files & Folders: kebab-case or camelCase as appropriate for the context (e.g., api/auth/\[...nextauth\]/route.ts).
4.4. Commenting:
\* Use JSDoc /\*\* ... \*/ for all components, hooks, and complex functions to explain their purpose, props/parameters, and return values.
\* Use inline comments // to explain complex or non-obvious logic (the "why," not the "what").

### **Rule 5: Security Mandates**

5.1. No Hardcoded Secrets: Never write API keys, database URLs, or any other secrets directly in the code. They MUST be loaded from environment variables (.env.local).
5.2. Input Validation: Use a library like Zod to validate all API request bodies and parameters. Treat all user input as untrusted.
5.3. ORM Usage: Always use the Prisma Client for database queries to prevent SQL injection. Do not write raw SQL queries unless absolutely necessary and properly parameterized.
5.4. Authentication Checks: Every API route that requires authentication must verify the user's session or API key before executing its logic.

### **Rule 6: AI Assistant \- Operational Protocol**

6.1. Acknowledge Context First: At the beginning of every session, confirm that you have reviewed these rules.
6.2. Request Current Code: Before writing or modifying any code, you MUST ask for the latest version of the relevant file(s). Do not work from an assumed state.
6.3. Ask, Don't Assume: If a request is ambiguous or conflicts with these rules, you MUST ask for clarification. Do not make assumptions about functionality.
6.4. Explain Your Work: After providing code, you MUST provide a brief, clear explanation of the changes you made and which rules they adhere to.
6.5. Work Incrementally: For complex requests, break the problem down into smaller steps and address them one at a time.
6.6. Admit Limitations: If you cannot fulfill a request perfectly while adhering to these rules, state the limitation and offer the best possible alternative.