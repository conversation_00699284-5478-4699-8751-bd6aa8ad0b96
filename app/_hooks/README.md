# Custom Hooks

This directory contains custom React hooks for the Unfurl application.

## `useDbUser`

A custom hook for fetching and managing database user data in client components.

### Usage

```tsx
import { useDbUser } from '@/hooks/use-db-user';

function MyComponent() {
    const { dbUser, loading, error, refetch } = useDbUser();

    if (loading) {
        return <div>Loading user data...</div>;
    }

    if (error) {
        return <div>Error: {error}</div>;
    }

    if (!dbUser) {
        return <div>No user data available</div>;
    }

    return (
        <div>
            <h2>Welcome, {dbUser.name}!</h2>
            <p>Credits: {dbUser.creditBalance}</p>
            <p>Plan: {dbUser.subscriptionPlan}</p>
            <button onClick={refetch}>Refresh Data</button>
        </div>
    );
}
```

### Return Values

- `dbUser`: The database user object with all user information including credits, subscription, etc.
- `loading`: Boolean indicating if the data is currently being fetched
- `error`: String containing error message if fetch failed, null otherwise
- `refetch`: Function to manually refetch the user data

### Features

- Automatically fetches user data when Clerk user is available
- Provides loading states and error handling
- Includes a refetch function for manual data refresh
- Handles authentication state changes automatically

### Server Components

For server components, use the `getCurrentDbUser()` function from `@/lib/auth` instead:

```tsx
import { getCurrentDbUser } from '@/lib/auth';

export default async function ServerComponent() {
    const dbUser = await getCurrentDbUser();
    
    if (!dbUser) {
        return <div>User not found</div>;
    }
    
    return <div>Welcome, {dbUser.name}!</div>;
}
```

## `useIsMobile`

A hook for detecting mobile screen sizes.

### Usage

```tsx
import { useIsMobile } from '@/hooks/use-mobile';

function ResponsiveComponent() {
    const isMobile = useIsMobile();
    
    return (
        <div>
            {isMobile ? 'Mobile View' : 'Desktop View'}
        </div>
    );
}
```
