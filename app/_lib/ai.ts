import { callMistralOCRAPI } from './providers/mistral-ocr';

export type AIResult = {
  pageCount: number;
  extractedData: object;
  confidence?: number;
};

export async function processDocumentWithAI(
  jobId: string,
  provider: 'mistral-ocr' | 'openai-gpt4o' = 'mistral-ocr'
): Promise<AIResult> {
  switch (provider) {
    case 'mistral-ocr':
      return callMistralOCRAPI(jobId);
    default:
      throw new Error('Unsupported AI provider');
  }
}
