import { cn } from '@/lib/utils';

export function Logo({ className, ...props }: React.ComponentProps<'svg'>) {
  return (
    <svg
      width="28"
      height="28"
      viewBox="0 0 24 24"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={cn('text-indigo-600', className)}
      {...props}
    >
      <path
        d="M4 22V8.5C4 7.22876 4 6.59313 4.25301 6.0754C4.47467 5.62137 4.82137 5.27467 5.2754 5.05301C5.79313 4.8 6.42876 4.8 7.7 4.8H12M4 22L8 18M4 22L1 19"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M20 2H10.5C9.22876 2 8.59313 2 8.0754 2.25301C7.62137 2.47467 7.27467 2.82137 7.05301 3.2754C6.8 3.79313 6.8 4.42876 6.8 5.7V15.5C6.8 16.7712 6.8 17.4069 7.05301 17.9246C7.27467 18.3786 7.62137 18.7253 8.0754 18.947C8.59313 19.2 9.22876 19.2 10.5 19.2H14.5M20 2L16 6M20 2L23 5"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
