<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="UTF-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0" />
        <title>Unfurl - Intelligent Data Extraction, Simplified.</title>
        <script src="https://cdn.tailwindcss.com"></script>
        <link
            href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap"
            rel="stylesheet"
        />
        <script src="https://unpkg.com/lucide@latest"></script>
        <style>
            body {
                font-family: 'Inter', sans-serif;
            }
            .gradient-bg {
                background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            }
        </style>
    </head>
    <body class="bg-white text-gray-800">
        <!-- Header -->
        <header
            class="fixed w-full bg-white/80 backdrop-blur-sm z-50 border-b border-gray-200"
        >
            <div
                class="container mx-auto px-6 py-4 flex justify-between items-center"
            >
                <!-- Logo -->
                <a
                    href="#"
                    class="flex items-center space-x-2 text-2xl font-bold text-gray-900"
                >
                    <svg
                        width="32"
                        height="32"
                        viewBox="0 0 24 24"
                        fill="none"
                        xmlns="http://www.w3.org/2000/svg"
                        class="text-indigo-600"
                    >
                        <path
                            d="M4 22V8.5C4 7.22876 4 6.59313 4.25301 6.0754C4.47467 5.62137 4.82137 5.27467 5.2754 5.05301C5.79313 4.8 6.42876 4.8 7.7 4.8H12M4 22L8 18M4 22L1 19"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                        <path
                            d="M20 2H10.5C9.22876 2 8.59313 2 8.0754 2.25301C7.62137 2.47467 7.27467 2.82137 7.05301 3.2754C6.8 3.79313 6.8 4.42876 6.8 5.7V15.5C6.8 16.7712 6.8 17.4069 7.05301 17.9246C7.27467 18.3786 7.62137 18.7253 8.0754 18.947C8.59313 19.2 9.22876 19.2 10.5 19.2H14.5M20 2L16 6M20 2L23 5"
                            stroke="currentColor"
                            stroke-width="2"
                            stroke-linecap="round"
                            stroke-linejoin="round"
                        />
                    </svg>
                    <span>Unfurl</span>
                </a>
                <!-- Desktop Navigation -->
                <nav class="hidden md:flex items-center space-x-8">
                    <a
                        href="#features"
                        class="text-gray-600 hover:text-indigo-600"
                    >
                        Features
                    </a>
                    <a
                        href="#use-cases"
                        class="text-gray-600 hover:text-indigo-600"
                    >
                        Use Cases
                    </a>
                    <a href="#api" class="text-gray-600 hover:text-indigo-600">
                        API
                    </a>
                    <a
                        href="#pricing"
                        class="text-gray-600 hover:text-indigo-600"
                    >
                        Pricing
                    </a>
                </nav>
                <!-- Action Buttons -->
                <div class="hidden md:flex items-center space-x-4">
                    <a href="#" class="text-gray-600 hover:text-indigo-600">
                        Log In
                    </a>
                    <a
                        href="#"
                        class="bg-indigo-600 text-white px-5 py-2 rounded-lg shadow hover:bg-indigo-700 transition"
                    >
                        Get Started Free
                    </a>
                </div>
                <!-- Mobile Menu Button -->
                <button id="mobile-menu-button" class="md:hidden">
                    <i data-lucide="menu"></i>
                </button>
            </div>
            <!-- Mobile Menu -->
            <div id="mobile-menu" class="hidden md:hidden px-6 pb-4 space-y-2">
                <a
                    href="#features"
                    class="block text-gray-600 hover:text-indigo-600"
                >
                    Features
                </a>
                <a
                    href="#use-cases"
                    class="block text-gray-600 hover:text-indigo-600"
                >
                    Use Cases
                </a>
                <a
                    href="#api"
                    class="block text-gray-600 hover:text-indigo-600"
                >
                    API
                </a>
                <a
                    href="#pricing"
                    class="block text-gray-600 hover:text-indigo-600"
                >
                    Pricing
                </a>
                <div class="border-t border-gray-200 pt-4 space-y-2">
                    <a
                        href="#"
                        class="block text-gray-600 hover:text-indigo-600"
                    >
                        Log In
                    </a>
                    <a
                        href="#"
                        class="block bg-indigo-600 text-white text-center px-5 py-2 rounded-lg shadow hover:bg-indigo-700 transition"
                    >
                        Get Started Free
                    </a>
                </div>
            </div>
        </header>

        <main class="pt-24">
            <!-- Hero Section -->
            <section class="container mx-auto px-6 py-16 md:py-24 text-center">
                <h1
                    class="text-4xl md:text-6xl font-bold tracking-tight text-gray-900 leading-tight"
                >
                    From Document to Data,
                    <span class="text-indigo-600">Instantly.</span>
                </h1>
                <p
                    class="mt-4 text-lg md:text-xl text-gray-600 max-w-3xl mx-auto"
                >
                    Unfurl is the AI-powered platform that ends manual data
                    entry. Extract structured data from invoices, receipts, and
                    forms with incredible accuracy.
                </p>
                <p
                    class="mt-2 text-lg md:text-xl font-semibold text-gray-800 max-w-3xl mx-auto"
                >
                    Intelligent data extraction, simplified.
                </p>
                <div class="mt-8 flex justify-center items-center space-x-4">
                    <a
                        href="#"
                        class="bg-indigo-600 text-white px-8 py-3 rounded-lg shadow-lg hover:bg-indigo-700 transition-transform hover:scale-105"
                    >
                        Start Your Free Trial
                    </a>
                    <a
                        href="#api"
                        class="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
                    >
                        <span>Explore the API</span>
                        <i data-lucide="arrow-right" class="w-4 h-4"></i>
                    </a>
                </div>

                <!-- Hero Image -->
                <div
                    class="mt-16 max-w-4xl mx-auto p-4 bg-white rounded-xl shadow-2xl border border-gray-200"
                >
                    <div
                        class="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden"
                    >
                        <img
                            src="https://placehold.co/1200x675/E9EBFB/4F46E5?text=Document+Extraction+in+Action"
                            alt="Unfurl App Screenshot"
                            class="w-full h-full object-cover"
                        />
                    </div>
                </div>
            </section>

            <!-- Features Section -->
            <section id="features" class="py-20 bg-gray-50">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-12">
                        <h2 class="text-3xl md:text-4xl font-bold">
                            Automate Your Entire Workflow
                        </h2>
                        <p class="mt-4 text-lg text-gray-600">
                            Unfurl is more than just OCR. It's a complete data
                            extraction solution.
                        </p>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8">
                        <!-- Feature 1 -->
                        <div
                            class="bg-white p-8 rounded-lg shadow-lg border border-gray-100"
                        >
                            <div
                                class="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center"
                            >
                                <i data-lucide="scan-text"></i>
                            </div>
                            <h3 class="mt-6 text-xl font-bold">
                                Pre-built Models
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Instantly process common documents like
                                invoices, receipts, and passports with our
                                ready-to-use AI models. No setup required.
                            </p>
                        </div>
                        <!-- Feature 2 -->
                        <div
                            class="bg-white p-8 rounded-lg shadow-lg border border-gray-100"
                        >
                            <div
                                class="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center"
                            >
                                <i data-lucide="mouse-pointer-click"></i>
                            </div>
                            <h3 class="mt-6 text-xl font-bold">
                                Custom Templates
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Train Unfurl on your unique documents. Simply
                                click and label the data you need, and our AI
                                will learn to extract it automatically.
                            </p>
                        </div>
                        <!-- Feature 3 -->
                        <div
                            class="bg-white p-8 rounded-lg shadow-lg border border-gray-100"
                        >
                            <div
                                class="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center"
                            >
                                <i data-lucide="code-2"></i>
                            </div>
                            <h3 class="mt-6 text-xl font-bold">
                                Developer-Friendly API
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Integrate intelligent data extraction into your
                                own applications with our simple, powerful, and
                                well-documented REST API.
                            </p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- How it Works Section -->
            <section class="py-20">
                <div class="container mx-auto px-6">
                    <div class="text-center mb-16">
                        <h2 class="text-3xl md:text-4xl font-bold">
                            Get Structured Data in 3 Simple Steps
                        </h2>
                    </div>
                    <div class="grid md:grid-cols-3 gap-8 text-center relative">
                        <!-- Step 1 -->
                        <div class="flex flex-col items-center">
                            <div
                                class="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg"
                            >
                                1
                            </div>
                            <i
                                data-lucide="upload-cloud"
                                class="w-20 h-20 text-indigo-500 my-6"
                            ></i>
                            <h3 class="text-xl font-semibold">
                                Upload Document
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Drag & drop a PDF, JPG, or PNG file. Or send it
                                via our API.
                            </p>
                        </div>
                        <!-- Step 2 -->
                        <div class="flex flex-col items-center">
                            <div
                                class="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg"
                            >
                                2
                            </div>
                            <i
                                data-lucide="wand-2"
                                class="w-20 h-20 text-indigo-500 my-6"
                            ></i>
                            <h3 class="text-xl font-semibold">
                                Let AI Work Its Magic
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Unfurl's AI reads, understands, and structures
                                your data in seconds.
                            </p>
                        </div>
                        <!-- Step 3 -->
                        <div class="flex flex-col items-center">
                            <div
                                class="bg-indigo-600 text-white rounded-full w-16 h-16 flex items-center justify-center text-2xl font-bold shadow-lg"
                            >
                                3
                            </div>
                            <i
                                data-lucide="download-cloud"
                                class="w-20 h-20 text-indigo-500 my-6"
                            ></i>
                            <h3 class="text-xl font-semibold">
                                Export & Integrate
                            </h3>
                            <p class="mt-2 text-gray-600">
                                Download as CSV/JSON or use the API to send data
                                to your other apps.
                            </p>
                        </div>
                        <!-- Connecting line for desktop -->
                        <div
                            class="hidden md:block absolute top-8 left-0 w-full h-0.5 bg-gray-200 -z-10"
                        ></div>
                    </div>
                </div>
            </section>

            <!-- CTA Section -->
            <section class="gradient-bg">
                <div class="container mx-auto px-6 py-20 text-center">
                    <h2 class="text-3xl md:text-4xl font-bold text-gray-900">
                        Ready to Stop Typing?
                    </h2>
                    <p class="mt-4 text-lg text-gray-700 max-w-2xl mx-auto">
                        Sign up today and get 50 free document extractions. No
                        credit card required.
                    </p>
                    <div class="mt-8">
                        <a
                            href="#"
                            class="bg-white text-indigo-600 font-bold px-10 py-4 rounded-lg shadow-xl hover:bg-gray-100 transition-transform hover:scale-105"
                        >
                            Claim Your Free Credits
                        </a>
                    </div>
                </div>
            </section>
        </main>

        <!-- Footer -->
        <footer class="bg-gray-900 text-white">
            <div class="container mx-auto px-6 py-12">
                <div class="grid md:grid-cols-4 gap-8">
                    <!-- About -->
                    <div>
                        <h4 class="text-lg font-bold">Unfurl</h4>
                        <p class="mt-4 text-gray-400">
                            Intelligent data extraction, simplified.
                        </p>
                    </div>
                    <!-- Links -->
                    <div>
                        <h5 class="font-semibold tracking-wider uppercase">
                            Product
                        </h5>
                        <nav class="mt-4 space-y-2">
                            <a
                                href="#features"
                                class="block text-gray-400 hover:text-white"
                            >
                                Features
                            </a>
                            <a
                                href="#pricing"
                                class="block text-gray-400 hover:text-white"
                            >
                                Pricing
                            </a>
                            <a
                                href="#api"
                                class="block text-gray-400 hover:text-white"
                            >
                                API Docs
                            </a>
                        </nav>
                    </div>
                    <!-- Company -->
                    <div>
                        <h5 class="font-semibold tracking-wider uppercase">
                            Company
                        </h5>
                        <nav class="mt-4 space-y-2">
                            <a
                                href="#"
                                class="block text-gray-400 hover:text-white"
                            >
                                About Us
                            </a>
                            <a
                                href="#"
                                class="block text-gray-400 hover:text-white"
                            >
                                Contact
                            </a>
                            <a
                                href="#"
                                class="block text-gray-400 hover:text-white"
                            >
                                Careers
                            </a>
                        </nav>
                    </div>
                    <!-- Legal -->
                    <div>
                        <h5 class="font-semibold tracking-wider uppercase">
                            Legal
                        </h5>
                        <nav class="mt-4 space-y-2">
                            <a
                                href="#"
                                class="block text-gray-400 hover:text-white"
                            >
                                Privacy Policy
                            </a>
                            <a
                                href="#"
                                class="block text-gray-400 hover:text-white"
                            >
                                Terms of Service
                            </a>
                        </nav>
                    </div>
                </div>
                <div
                    class="mt-12 border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center"
                >
                    <p class="text-gray-500">
                        &copy; 2025 Unfurl, Inc. All rights reserved.
                    </p>
                    <div class="flex space-x-4 mt-4 md:mt-0">
                        <a href="#" class="text-gray-500 hover:text-white">
                            <i data-lucide="twitter"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-white">
                            <i data-lucide="github"></i>
                        </a>
                        <a href="#" class="text-gray-500 hover:text-white">
                            <i data-lucide="linkedin"></i>
                        </a>
                    </div>
                </div>
            </div>
        </footer>

        <script>
            lucide.createIcons();

            // Mobile menu toggle
            const mobileMenuButton =
                document.getElementById('mobile-menu-button');
            const mobileMenu = document.getElementById('mobile-menu');
            mobileMenuButton.addEventListener('click', () => {
                mobileMenu.classList.toggle('hidden');
            });
        </script>
    </body>
</html>
