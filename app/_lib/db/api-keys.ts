import { prisma } from '../prisma';
import type { <PERSON><PERSON><PERSON><PERSON> } from './types';

// API Key operations
export const apiKeyOperations = {
  async create(data: { name: string; key: string; userId: string }) {
    return prisma.apiKey.create({
      data,
    });
  },

  async findBy<PERSON>ey(key: string) {
    return prisma.apiKey.findUnique({
      where: { key },
      include: { user: true },
    });
  },

  async findByUser(userId: string): Promise<ApiKey[]> {
    return prisma.apiKey.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async updateUsage(keyId: string) {
    return prisma.apiKey.update({
      where: { id: keyId },
      data: {
        requestCount: { increment: 1 },
        lastUsedAt: new Date(),
      },
    });
  },
};
