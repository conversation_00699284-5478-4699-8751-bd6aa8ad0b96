import { jobOperations } from './jobs';
import { userOperations } from './users';
import { creditOperations } from './credits';
import type { JobStatus } from './types';

// Helper functions for process route
export async function getJobById(jobId: string) {
  return jobOperations.findById(jobId);
}

export async function updateJobStatus(
  jobId: string,
  status: JobStatus,
  error?: string
) {
  return jobOperations.updateStatus(jobId, status, error);
}

export async function saveJobResult(
  jobId: string,
  result: Record<string, unknown>,
  userId: string,
  documentId: string,
  confidence?: number
) {
  return jobOperations.saveResult(
    jobId,
    result,
    userId,
    documentId,
    confidence
  );
}

export async function getUserById(userId: string) {
  return userOperations.findById(userId);
}

export async function decrementUserCredits(userId: string, amount: number) {
  return creditOperations.consumeCredits(userId, amount, 'Document processing');
}
