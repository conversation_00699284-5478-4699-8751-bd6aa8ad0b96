import { prisma } from '../prisma';
import type { JobStatus, JobType } from './types';

// Job operations
export const jobOperations = {
  async create(data: {
    userId: string;
    documentId: string;
    type: JobType;
    templateId?: string;
  }) {
    return prisma.job.create({
      data,
    });
  },

  async updateStatus(jobId: string, status: JobStatus, error?: string) {
    const updateData: Partial<{
      status: JobStatus;
      startedAt: Date;
      completedAt: Date;
      error: string;
    }> = { status };

    if (status === 'RUNNING') {
      updateData.startedAt = new Date();
    } else if (status === 'COMPLETED' || status === 'FAILED') {
      updateData.completedAt = new Date();
    }

    if (error) {
      updateData.error = error;
    }

    return prisma.job.update({
      where: { id: jobId },
      data: updateData,
    });
  },

  async updateProgress(jobId: string, progress: number) {
    return prisma.job.update({
      where: { id: jobId },
      data: { progress },
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.job.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        document: true,
        template: true,
      },
    });
  },

  async findById(jobId: string) {
    return prisma.job.findUnique({
      where: { id: jobId },
      include: { document: true, template: true },
    });
  },

  async saveResult(
    jobId: string,
    result: Record<string, unknown>,
    userId: string,
    documentId: string,
    confidence?: number
  ) {
    return prisma.extractedData.create({
      data: {
        data: result as any, // Prisma expects JsonValue
        confidence,
        userId,
        documentId,
        jobId,
      },
    });
  },
};
