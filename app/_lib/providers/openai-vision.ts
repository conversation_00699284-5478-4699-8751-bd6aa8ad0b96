// OpenAI Vision API provider implementation
import type { AIResult } from '../ai';

const OPENAI_API_KEY = process.env.OPENAI_API_KEY;
const OPENAI_API_URL = 'https://api.openai.com/v1/chat/completions';

export async function callOpenAIVisionAPI(
  documentUrl: string
): Promise<AIResult> {
  if (!OPENAI_API_KEY) {
    throw new Error('OPENAI_API_KEY is not set in environment variables');
  }

  // Compose the prompt for vision extraction
  const messages = [
    {
      role: 'system',
      content:
        'You are a document extraction assistant. Extract all structured data from the provided image or PDF.',
    },
    {
      role: 'user',
      content: [
        {
          type: 'text',
          text: 'Extract all structured data from this document.',
        },
        {
          type: 'image_url',
          image_url: documentUrl,
        },
      ],
    },
  ];

  const response = await fetch(OPENAI_API_URL, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${OPENAI_API_KEY}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      model: 'gpt-4-vision-preview',
      messages,
      max_tokens: 2048,
    }),
  });

  if (!response.ok) {
    const error = await response.text();
    throw new Error(`OpenAI API error: ${error}`);
  }

  const data = await response.json();
  const content = data.choices?.[0]?.message?.content || '';

  // Try to parse JSON from the response, fallback to raw text
  let extracted: any = content;
  try {
    extracted = JSON.parse(content);
  } catch {}

  return {
    pages: 1, // OpenAI does not return page count; adjust if needed
    data: extracted,
    confidence: undefined, // OpenAI does not return confidence
  };
}
