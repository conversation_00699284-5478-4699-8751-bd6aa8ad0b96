'use client';

import { DocumentListPanel } from '@/components/document-list-panel';
import { DocumentViewer } from '@/components/document-viewer';
import { DataExtractionPanel } from '@/components/data-extraction-panel';
import { useState } from 'react';

export default function DocumentsClientPage() {
  const [selectedDocumentId, setSelectedDocumentId] = useState<string>();
  const [activeField, setActiveField] = useState<string>();

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocumentId(documentId);
  };

  const handleFieldFocus = (field: string) => {
    setActiveField(field);
  };

  const handleFieldBlur = () => {
    setActiveField(undefined);
  };

  return (
    <div className="flex-1 flex flex-col md:flex-row overflow-hidden">
      <DocumentListPanel
        selectedDocumentId={selectedDocumentId}
        onDocumentSelect={handleDocumentSelect}
      />
      <DocumentViewer
        documentId={selectedDocumentId}
        activeField={activeField}
      />
      <DataExtractionPanel
        documentId={selectedDocumentId}
        onFieldFocus={handleFieldFocus}
        onFieldBlur={handleFieldBlur}
      />
    </div>
  );
}
