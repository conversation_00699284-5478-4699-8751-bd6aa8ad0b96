import { ScanTex<PERSON>, Mouse<PERSON>ointer<PERSON>lick, Code2 } from 'lucide-react';

export default function Features() {
    return (
        <section id="features" className="py-20 bg-gray-50">
            <div className="container mx-auto px-6">
                <div className="text-center mb-12">
                    <h2 className="text-3xl md:text-4xl font-bold">
                        Automate Your Entire Workflow
                    </h2>
                    <p className="mt-4 text-lg text-gray-600">
                        Unfurl is more than just OCR. It&apos;s a complete data
                        extraction solution.
                    </p>
                </div>
                <div className="grid md:grid-cols-3 gap-8">
                    {/* Feature 1 */}
                    <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                        <div className="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center">
                            <ScanText />
                        </div>
                        <h3 className="mt-6 text-xl font-bold">
                            Pre-built Models
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Instantly process common documents like invoices,
                            receipts, and passports with our ready-to-use AI
                            models. No setup required.
                        </p>
                    </div>
                    {/* Feature 2 */}
                    <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                        <div className="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center">
                            <MousePointerClick />
                        </div>
                        <h3 className="mt-6 text-xl font-bold">
                            Custom Templates
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Train Unfurl on your unique documents. Simply click
                            and label the data you need, and our AI will learn
                            to extract it automatically.
                        </p>
                    </div>
                    {/* Feature 3 */}
                    <div className="bg-white p-8 rounded-lg shadow-lg border border-gray-100">
                        <div className="bg-indigo-100 text-indigo-600 rounded-lg w-12 h-12 flex items-center justify-center">
                            <Code2 />
                        </div>
                        <h3 className="mt-6 text-xl font-bold">
                            Developer-Friendly API
                        </h3>
                        <p className="mt-2 text-gray-600">
                            Integrate intelligent data extraction into your own
                            applications with our simple, powerful, and
                            well-documented REST API.
                        </p>
                    </div>
                </div>
            </div>
        </section>
    );
}
