import { NextResponse } from 'next/server';
import { getCurrentDbUser } from '@/lib/auth';

export async function GET() {
    try {
        const dbUser = await getCurrentDbUser();
        
        if (!dbUser) {
            return NextResponse.json(
                { error: 'User not found' },
                { status: 404 }
            );
        }

        return NextResponse.json(dbUser);
    } catch (error) {
        console.error('Error fetching user:', error);
        return NextResponse.json(
            { error: 'Internal server error' },
            { status: 500 }
        );
    }
}
