import { prisma } from '../prisma';
import type { SubscriptionPlan } from './types';

// User operations
export const userOperations = {
  async findByEmail(email: string) {
    return prisma.user.findUnique({
      where: { email },
      include: {
        creditTransactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });
  },

  async findById(userId: string) {
    return prisma.user.findUnique({
      where: { id: userId },
      include: {
        creditTransactions: {
          orderBy: { createdAt: 'desc' },
          take: 10,
        },
      },
    });
  },

  async create(data: {
    id: string; // Clerk user ID
    email: string;
    name?: string;
    imageUrl?: string;
  }) {
    return prisma.user.create({
      data,
    });
  },

  async update(
    userId: string,
    data: {
      email?: string;
      name?: string;
      imageUrl?: string;
    }
  ) {
    return prisma.user.update({
      where: { id: userId },
      data,
    });
  },

  async delete(userId: string) {
    return prisma.user.delete({
      where: { id: userId },
    });
  },

  async updateSubscription(
    userId: string,
    plan: SubscriptionPlan,
    stripeCustomerId?: string,
    stripeSubscriptionId?: string
  ) {
    // Calculate new monthly credit limit based on plan
    const creditLimits = {
      FREE: 10,
      STARTER: 500,
      PROFESSIONAL: 2000,
      ENTERPRISE: 10000,
    };

    return prisma.user.update({
      where: { id: userId },
      data: {
        subscriptionPlan: plan,
        stripeCustomerId,
        stripeSubscriptionId,
        subscriptionStatus: 'active',
        monthlyCreditsLimit: creditLimits[plan],
      },
    });
  },

  async checkCreditBalance(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        creditBalance: true,
        monthlyCreditsUsed: true,
        monthlyCreditsLimit: true,
        subscriptionPlan: true,
      },
    });
    return user;
  },

  async hasCredits(userId: string, requiredCredits: number) {
    const user = await this.checkCreditBalance(userId);
    return user ? user.creditBalance >= requiredCredits : false;
  },
};
