import { Mistral } from '@mistralai/mistralai';
import type { AIResult } from '../ai';
import type { Job, Template } from '@prisma/client';
import { jobOperations } from '../db';

const apiKey = process.env.MISTRAL_API_KEY;

export async function callMistralOCRAPI(jobId: string): Promise<AIResult> {
  if (!apiKey) {
    throw new Error('MISTRAL_API_KEY is not set in environment variables');
  }

  const job = await jobOperations.findById(jobId);
  if (!job) {
    throw new Error('Job not found');
  }

  // TODO: use alternative method when template is not provided
  try {
    const mistral = new Mistral({ apiKey: apiKey });

    const ocrResult = await mistral.ocr.process({
      model: 'mistral-ocr-latest',
      pages: Array.from({ length: 8 }, (_, i) => i),
      document: {
        type: 'document_url',
        documentUrl: job.document.storageUrl,
      },
      documentAnnotationFormat: {
        type: 'json_schema',
        jsonSchema: {
          name: 'document_annotation',
          schemaDefinition: {
            type: 'object',
            properties: job.template?.fields,
            required: [],
          },
          strict: true,
        },
      },
    });

    return {
      pages: pagesProcessed,
      data: structuredData,
      confidence,
    };
  } catch (error: any) {
    console.error('Mistral OCR API error:', error);
    throw new Error(`Mistral OCR processing failed: ${error.message}`);
  }
}

/**
 * Extracts plain text from Mistral OCR result
 */
function extractTextFromOCRResult(ocrResult: any): string {
  if (!ocrResult.pages || !Array.isArray(ocrResult.pages)) {
    return '';
  }

  return ocrResult.pages
    .map((page: any) => page.markdown || '')
    .join('\n\n')
    .trim();
}

/**
 * Performs template-based data extraction using Mistral's chat completion
 */
async function performTemplateBasedExtraction(
  mistral: Mistral,
  text: string,
  template: Template,
  model: string
): Promise<{ data: any; confidence: number }> {
  try {
    // Build extraction prompt based on template fields
    const extractionPrompt = buildExtractionPrompt(text, template);

    // Use Mistral's chat completion for structured extraction
    const response = await mistral.chat.complete({
      model,
      messages: [
        {
          role: 'system',
          content:
            'You are a precise data extraction assistant. Extract only the requested fields from the provided text. Return valid JSON only, no additional text or explanations.',
        },
        {
          role: 'user',
          content: extractionPrompt,
        },
      ],
      temperature: 0.1, // Low temperature for consistent extraction
      maxTokens: 2048,
    });

    const messageContent = response.choices?.[0]?.message?.content;
    const extractedContent =
      typeof messageContent === 'string' ? messageContent : '{}';

    // Parse the extracted data
    let extractedData: any = {};
    try {
      extractedData = JSON.parse(extractedContent);
    } catch (parseError) {
      console.warn(
        'Failed to parse extraction result as JSON:',
        extractedContent
      );
      // Fallback: try to extract data using regex patterns
      extractedData = fallbackExtraction(text, template);
    }

    // Calculate confidence based on how many required fields were extracted
    const confidence = calculateExtractionConfidence(extractedData, template);

    return {
      data: extractedData,
      confidence,
    };
  } catch (error: any) {
    console.error('Template-based extraction error:', error);
    // Return fallback extraction
    return {
      data: fallbackExtraction(text, template),
      confidence: 0.3, // Low confidence for fallback
    };
  }
}

/**
 * Builds extraction prompt based on template fields
 */
function buildExtractionPrompt(text: string, template: Template): string {
  const fields = template.fields as any;

  // Build field descriptions
  const fieldDescriptions = Object.entries(fields)
    .map(([fieldName, fieldConfig]: [string, any]) => {
      const required = fieldConfig.required ? ' (required)' : ' (optional)';
      const type = fieldConfig.type || 'string';
      return `- ${fieldName}: ${type}${required}`;
    })
    .join('\n');

  return `Extract the following fields from this ${template.documentType} document:

${fieldDescriptions}

Document text:
${text}

Return the extracted data as a JSON object with the field names as keys. If a field is not found, use null as the value. Example format:
{
  "field1": "value1",
  "field2": null,
  "field3": 123
}`;
}

/**
 * Fallback extraction using simple regex patterns
 */
function fallbackExtraction(text: string, template: Template): any {
  const fields = template.fields as any;
  const result: any = {};

  Object.entries(fields).forEach(([fieldName, fieldConfig]: [string, any]) => {
    const type = fieldConfig.type || 'string';

    // Simple pattern matching based on field name and type
    switch (type) {
      case 'number':
      case 'currency':
        result[fieldName] = extractNumber(text, fieldName);
        break;
      case 'date':
        result[fieldName] = extractDate(text, fieldName);
        break;
      case 'email':
        result[fieldName] = extractEmail(text);
        break;
      case 'phone':
        result[fieldName] = extractPhone(text);
        break;
      default:
        result[fieldName] = extractString(text, fieldName);
    }
  });

  return result;
}

/**
 * Calculates extraction confidence based on template requirements
 */
function calculateExtractionConfidence(
  extractedData: any,
  template: Template
): number {
  const fields = template.fields as any;
  const fieldNames = Object.keys(fields);
  const requiredFields = fieldNames.filter((name) => fields[name].required);

  if (fieldNames.length === 0) return 0.5;

  // Count successfully extracted fields
  const extractedFields = fieldNames.filter(
    (name) =>
      extractedData[name] !== null &&
      extractedData[name] !== undefined &&
      extractedData[name] !== ''
  );

  // Count successfully extracted required fields
  const extractedRequiredFields = requiredFields.filter(
    (name) =>
      extractedData[name] !== null &&
      extractedData[name] !== undefined &&
      extractedData[name] !== ''
  );

  // Base confidence from overall field extraction
  const overallConfidence = extractedFields.length / fieldNames.length;

  // Penalty for missing required fields
  const requiredConfidence =
    requiredFields.length > 0
      ? extractedRequiredFields.length / requiredFields.length
      : 1.0;

  // Weighted average (required fields are more important)
  return (
    Math.round((overallConfidence * 0.4 + requiredConfidence * 0.6) * 100) / 100
  );
}

// Utility functions for pattern-based extraction

/**
 * Extracts numbers from text based on field name context
 */
function extractNumber(text: string, fieldName: string): number | null {
  // Look for numbers near the field name
  const fieldPattern = new RegExp(`${fieldName}[:\\s]*([\\d,]+\\.?\\d*)`, 'i');
  const match = text.match(fieldPattern);
  if (match) {
    const numStr = match[1].replace(/,/g, '');
    const num = parseFloat(numStr);
    return isNaN(num) ? null : num;
  }

  // Fallback: look for currency amounts if field suggests money
  if (
    fieldName.toLowerCase().includes('amount') ||
    fieldName.toLowerCase().includes('total') ||
    fieldName.toLowerCase().includes('price')
  ) {
    const currencyPattern = /[\$£€¥]?\s*([0-9,]+\.?\d*)/g;
    const matches = Array.from(text.matchAll(currencyPattern));
    if (matches.length > 0) {
      const numStr = matches[matches.length - 1][1].replace(/,/g, '');
      const num = parseFloat(numStr);
      return isNaN(num) ? null : num;
    }
  }

  return null;
}

/**
 * Extracts dates from text based on field name context
 */
function extractDate(text: string, fieldName: string): string | null {
  // Look for dates near the field name
  const fieldPattern = new RegExp(
    `${fieldName}[:\\s]*([0-9]{1,2}[/-][0-9]{1,2}[/-][0-9]{2,4})`,
    'i'
  );
  const match = text.match(fieldPattern);
  if (match) {
    return normalizeDate(match[1]);
  }

  // Fallback: look for common date patterns
  const datePatterns = [
    /\b(\d{1,2}[/-]\d{1,2}[/-]\d{2,4})\b/g,
    /\b(\d{4}[/-]\d{1,2}[/-]\d{1,2})\b/g,
    /\b([A-Za-z]{3,9}\s+\d{1,2},?\s+\d{4})\b/g,
  ];

  for (const pattern of datePatterns) {
    const matches = Array.from(text.matchAll(pattern));
    if (matches.length > 0) {
      return normalizeDate(matches[0][1]);
    }
  }

  return null;
}

/**
 * Extracts email addresses from text
 */
function extractEmail(text: string): string | null {
  const emailPattern = /\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b/;
  const match = text.match(emailPattern);
  return match ? match[0] : null;
}

/**
 * Extracts phone numbers from text
 */
function extractPhone(text: string): string | null {
  const phonePatterns = [
    /\b\+?1?[-.\s]?\(?([0-9]{3})\)?[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b/,
    /\b([0-9]{3})[-.\s]?([0-9]{3})[-.\s]?([0-9]{4})\b/,
    /\b\+?([0-9]{1,3})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})[-.\s]?([0-9]{3,4})\b/,
  ];

  for (const pattern of phonePatterns) {
    const match = text.match(pattern);
    if (match) {
      return match[0];
    }
  }

  return null;
}

/**
 * Extracts string values from text based on field name context
 */
function extractString(text: string, fieldName: string): string | null {
  // Look for text near the field name
  const fieldPattern = new RegExp(`${fieldName}[:\\s]*([^\\n\\r]{1,100})`, 'i');
  const match = text.match(fieldPattern);
  if (match) {
    return match[1].trim();
  }

  return null;
}

/**
 * Normalizes date strings to ISO format
 */
function normalizeDate(dateStr: string): string {
  try {
    const date = new Date(dateStr);
    if (isNaN(date.getTime())) {
      return dateStr; // Return original if can't parse
    }
    return date.toISOString().split('T')[0]; // Return YYYY-MM-DD format
  } catch {
    return dateStr;
  }
}
