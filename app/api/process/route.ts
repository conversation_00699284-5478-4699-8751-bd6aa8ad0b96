import { NextRequest, NextResponse } from 'next/server';
import {
  updateJobStatus,
  getUserById,
  decrementUserCredits,
  jobOperations,
} from '@/lib/db';
import { verifyQueueSecret } from '@/lib/utils';
import { processDocumentWithAI } from '@/lib/ai';

// Only allow requests from the queueing service
export async function POST(req: NextRequest) {
  const secret = req.headers.get('x-queue-secret');
  if (!verifyQueueSecret(secret)) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { jobId } = await req.json();
  if (!jobId) {
    return NextResponse.json({ error: 'Missing jobId' }, { status: 400 });
  }

  // Fetch job details with template
  const job = await jobOperations.findById(jobId);
  if (!job) {
    return NextResponse.json({ error: 'Job not found' }, { status: 404 });
  }

  try {
    const aiResult = await processDocumentWithAI(jobId);
    // Decrement user credits
    const user = await getUserById(job.userId);
    const pagesProcessed = aiResult.pageCount || 1;
    await decrementUserCredits(user!.id, pagesProcessed);
    // Save result and update job status
    await jobOperations.saveResult(
      jobId,
      aiResult.data,
      user!.id,
      job!.documentId,
      aiResult!.confidence
    );
    await updateJobStatus(jobId, 'COMPLETED');
    return NextResponse.json({ success: true });
  } catch (err: unknown) {
    const errorMessage =
      err instanceof Error ? err.message : 'Processing failed';
    await updateJobStatus(jobId, 'FAILED', errorMessage);
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
