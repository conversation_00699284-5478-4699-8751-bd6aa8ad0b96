import { ArrowRight } from 'lucide-react';

export default function Hero() {
    return (
        <section className="container mx-auto px-6 py-16 md:py-24 text-center">
            <h1 className="text-4xl md:text-6xl font-bold tracking-tight text-gray-900 leading-tight">
                From Document to Data,{' '}
                <span className="text-indigo-600">Instantly.</span>
            </h1>
            <p className="mt-4 text-lg md:text-xl text-gray-600 max-w-3xl mx-auto">
                Unfurl is the AI-powered platform that ends manual data entry.
                Extract structured data from invoices, receipts, and forms with
                incredible accuracy.
            </p>
            <p className="mt-2 text-lg md:text-xl font-semibold text-gray-800 max-w-3xl mx-auto">
                Intelligent data extraction, simplified.
            </p>
            <div className="mt-8 flex justify-center items-center space-x-4">
                <a
                    href="#"
                    className="bg-indigo-600 text-white px-8 py-3 rounded-lg shadow-lg hover:bg-indigo-700 transition-transform hover:scale-105"
                >
                    Start Your Free Trial
                </a>
                <a
                    href="#api"
                    className="flex items-center space-x-2 text-gray-600 hover:text-gray-900"
                >
                    <span>Explore the API</span>
                    <ArrowRight className="w-4 h-4" />
                </a>
            </div>

            {/* Hero Image */}
            <div className="mt-16 max-w-4xl mx-auto p-4 bg-white rounded-xl shadow-2xl border border-gray-200">
                <div className="aspect-w-16 aspect-h-9 rounded-lg overflow-hidden">
                    <img
                        src="https://placehold.co/1200x675/E9EBFB/4F46E5?text=Document+Extraction+in+Action"
                        alt="Unfurl App Screenshot"
                        className="w-full h-full object-cover"
                    />
                </div>
            </div>
        </section>
    );
}
