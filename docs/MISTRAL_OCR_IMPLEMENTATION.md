# Mistral OCR Implementation

This document describes the implementation of Mistral OCR with template-based document annotation for the Unfurl application.

## Overview

The Mistral OCR implementation provides advanced document processing capabilities using Mistral AI's OCR and language models. It supports both basic OCR text extraction and sophisticated template-based structured data extraction.

## Features

- **OCR Processing**: Extract text from documents using Mistral's OCR capabilities
- **Template-Based Extraction**: Use predefined or custom templates to extract structured data
- **Confidence Scoring**: Get confidence scores for extraction quality
- **Fallback Extraction**: Regex-based fallback when AI extraction fails
- **Multi-format Support**: Process various document formats (PDF, images)

## Architecture

### Core Components

1. **Mistral OCR Provider** (`app/_lib/providers/mistral-ocr.ts`)
   - Main OCR processing logic
   - Template-based extraction
   - Confidence calculation
   - Error handling and fallbacks

2. **AI Interface** (`app/_lib/ai.ts`)
   - Unified interface for all AI providers
   - Supports Google Vision, OpenAI Vision, and Mistral OCR
   - Switchable model selection

3. **Template Model** (Prisma schema)
   - Database model for extraction templates
   - JSON field configuration
   - Public and user-specific templates

4. **Processing API** (`app/api/process/route.ts`)
   - Background job processing
   - Template integration
   - Credit management

## Template Configuration

Templates define the structure for data extraction using a JSON schema:

```json
{
  "field_name": {
    "type": "string|number|date|email|phone|array|object",
    "required": true|false,
    "description": "Optional field description"
  }
}
```

### Supported Field Types

- **string**: Text fields
- **number**: Numeric values (including currency)
- **date**: Date fields (automatically normalized)
- **email**: Email addresses
- **phone**: Phone numbers
- **array**: Lists of items
- **object**: Nested objects

### Pre-built Templates

The system includes pre-built templates for common document types:

1. **Invoice Template**
   - Invoice number, date, vendor info
   - Total amount, tax amount
   - Line items

2. **Receipt Template**
   - Merchant name, date, time
   - Total amount, payment method
   - Item details

3. **Business Card Template**
   - Name, title, company
   - Contact information
   - Address, website

## Usage Examples

### Basic OCR

```typescript
import { callMistralOCRAPI } from '@/lib/providers/mistral-ocr';

const result = await callMistralOCRAPI('https://example.com/document.pdf');
console.log(result.data.rawText);
```

### Template-Based Extraction

```typescript
import { callMistralOCRAPI } from '@/lib/providers/mistral-ocr';
import { templateOperations } from '@/lib/db';

// Get a template
const template = await templateOperations.findPublic()
  .then(templates => templates.find(t => t.documentType === 'invoice'));

// Process with template
const result = await callMistralOCRAPI('https://example.com/invoice.pdf', {
  template,
  model: 'pixtral-12b-2409'
});

console.log('Extracted data:', result.data);
console.log('Confidence:', result.confidence);
```

### Custom Template Creation

```typescript
import { templateOperations } from '@/lib/db';

const customTemplate = await templateOperations.create({
  name: 'Custom Form Template',
  description: 'Extract data from custom forms',
  documentType: 'custom_form',
  fields: {
    form_id: { type: 'string', required: true },
    submission_date: { type: 'date', required: true },
    applicant_name: { type: 'string', required: true },
    email: { type: 'email', required: false },
    score: { type: 'number', required: false }
  },
  userId: 'user-id'
});
```

## API Integration

### Processing Jobs

The system integrates with the existing job processing pipeline:

```typescript
// Create a job with template
const job = await jobOperations.create({
  userId: 'user-id',
  documentId: 'doc-id',
  type: 'EXTRACT',
  templateId: 'template-id'  // Optional template
});

// Process with Mistral OCR
const response = await fetch('/api/process', {
  method: 'POST',
  headers: { 'x-queue-secret': 'secret' },
  body: JSON.stringify({
    jobId: job.id,
    model: 'mistral'  // Use Mistral OCR
  })
});
```

## Configuration

### Environment Variables

```bash
MISTRAL_API_KEY=your_mistral_api_key_here
```

### Model Selection

The implementation uses `pixtral-12b-2409` for OCR processing and supports any Mistral chat model for structured extraction.

## Error Handling

The implementation includes comprehensive error handling:

1. **API Errors**: Proper error messages for API failures
2. **Parsing Errors**: Fallback to regex extraction when JSON parsing fails
3. **Template Errors**: Graceful handling of missing or invalid templates
4. **Network Errors**: Retry logic and timeout handling

## Confidence Scoring

Confidence scores are calculated based on:

- **Field Extraction Rate**: Percentage of template fields successfully extracted
- **Required Field Coverage**: Percentage of required fields extracted
- **Weighted Scoring**: Required fields have higher weight in confidence calculation

Formula:
```
confidence = (overall_extraction * 0.4) + (required_extraction * 0.6)
```

## Performance Considerations

1. **Caching**: Consider implementing response caching for repeated documents
2. **Rate Limiting**: Respect Mistral API rate limits
3. **Batch Processing**: Process multiple documents efficiently
4. **Error Recovery**: Implement retry logic for transient failures

## Testing

Example usage and testing can be found in:
- `app/_lib/examples/mistral-ocr-usage.ts`
- `app/_lib/examples/database-usage.ts`

## Future Enhancements

1. **Template Learning**: AI-assisted template creation from examples
2. **Validation Rules**: Custom validation logic for extracted data
3. **Multi-language Support**: Enhanced support for non-English documents
4. **Real-time Processing**: WebSocket-based real-time extraction updates
5. **Advanced Analytics**: Extraction quality analytics and reporting

## Troubleshooting

### Common Issues

1. **API Key Not Set**: Ensure `MISTRAL_API_KEY` is configured
2. **Template Not Found**: Verify template ID exists and is accessible
3. **Low Confidence**: Check document quality and template field definitions
4. **Parsing Errors**: Review template field types and document content

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=mistral-ocr
```

## Dependencies

- `@mistralai/mistralai`: Official Mistral AI TypeScript client
- `@prisma/client`: Database ORM for template management
- Standard Node.js libraries for text processing and validation
