import { NextRequest, NextResponse } from 'next/server';
import { requireDbUser } from '@/lib/auth';

export async function GET(req: NextRequest) {
  try {
    const user = await requireDbUser();
    
    return NextResponse.json({
      success: true,
      data: user,
    });
  } catch (error) {
    console.error('Error fetching user:', error);
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
