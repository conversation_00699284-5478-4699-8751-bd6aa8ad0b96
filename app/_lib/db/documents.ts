import { prisma } from '../prisma';
import type { DocumentStatus } from './types';

// Document operations
export const documentOperations = {
  async create(data: {
    filename: string;
    originalName: string;
    mimeType: string;
    size: number;
    storageUrl: string;
    userId: string;
    creditsUsed: number;
  }) {
    return prisma.document.create({
      data,
    });
  },

  async updateStatus(documentId: string, status: DocumentStatus) {
    return prisma.document.update({
      where: { id: documentId },
      data: { status },
    });
  },

  async findByUser(userId: string, limit = 10, offset = 0) {
    return prisma.document.findMany({
      where: { userId },
      orderBy: { uploadedAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        jobs: {
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });
  },

  // Calculate credits required for document processing
  calculateCreditsRequired(pageCount: number): number {
    // Flat rate: 1 credit = 1 page
    return pageCount;
  },

  // Helper to determine page count from document metadata
  async getPageCount(documentId: string): Promise<number> {
    // This would typically involve calling a service to analyze the document
    // For now, return a default or stored value
    const document = await prisma.document.findUnique({
      where: { id: documentId },
      select: { mimeType: true, size: true },
    });

    if (!document) return 1;

    // Simple heuristic based on file size and type
    // In production, you'd use a proper document analysis service
    if (document.mimeType === 'application/pdf') {
      // Rough estimate: 100KB per page for PDF
      return Math.max(1, Math.ceil(document.size / 100000));
    } else if (document.mimeType.startsWith('image/')) {
      // Images are always 1 page
      return 1;
    }

    return 1; // Default to 1 page
  },
};
