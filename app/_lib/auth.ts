import { auth, currentUser } from '@clerk/nextjs/server';
import { userOperations } from './db';

/**
 * Get the current authenticated user from Clerk
 * Returns null if not authenticated
 */
export async function getCurrentUser() {
  try {
    const user = await currentUser();
    return user;
  } catch (error) {
    console.error('Error getting current user:', error);
    return null;
  }
}

/**
 * Get the current user's ID from Clerk
 * Returns null if not authenticated
 */
export async function getCurrentUserId() {
  try {
    const { userId } = await auth();
    return userId;
  } catch (error) {
    console.error('Error getting current user ID:', error);
    return null;
  }
}

/**
 * Get the current user from the database
 * Creates the user if they don't exist
 */
export async function getCurrentDbUser() {
  try {
    const user = await getCurrentUser();
    if (!user) return null;

    let dbUser = await userOperations.findById(user.id);

    // If user doesn't exist in database, create them
    if (!dbUser) {
      await userOperations.create({
        id: user.id,
        email: user.emailAddresses[0]?.emailAddress || '',
        name: user.fullName || user.firstName || '',
        imageUrl: user.imageUrl,
      });

      // Fetch the newly created user with all relations
      dbUser = await userOperations.findById(user.id);
    }

    return dbUser;
  } catch (error) {
    console.error('Error getting current database user:', error);
    return null;
  }
}

/**
 * Require authentication - throws error if not authenticated
 * Use this in API routes that require authentication
 */
export async function requireAuth() {
  const { userId } = await auth();
  if (!userId) {
    throw new Error('Unauthorized');
  }
  return userId;
}

/**
 * Require authentication and return database user
 * Creates user if they don't exist
 * @throws Error if user is not authenticated
 */
export async function requireDbUser() {
  const userId = await requireAuth();
  const user = await getCurrentUser();

  if (!user) {
    throw new Error('User not found');
  }

  let dbUser = await userOperations.findById(userId);

  // If user doesn't exist in database, create them
  if (!dbUser) {
    await userOperations.create({
      id: user.id,
      email: user.emailAddresses[0]?.emailAddress || '',
      name: user.fullName || user.firstName || '',
      imageUrl: user.imageUrl,
    });

    // Fetch the newly created user with all relations
    dbUser = await userOperations.findById(userId);
  }

  // This should never be null at this point, but TypeScript doesn't know that
  if (!dbUser) {
    throw new Error('Failed to create or retrieve user');
  }

  return dbUser;
}
