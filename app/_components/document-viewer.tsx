"use client"

import { useState } from "react"

interface HighlightBox {
  field: string
  top: string
  left: string
  width: string
  height: string
}

// Mock highlight data - in real app this would come from API
const mockHighlights: HighlightBox[] = [
  {
    field: "invoice_number",
    top: "13.5%",
    left: "68%", 
    width: "22%",
    height: "3%",
  },
  {
    field: "vendor_name",
    top: "25.5%",
    left: "10%",
    width: "25%", 
    height: "3%",
  },
  {
    field: "invoice_date",
    top: "16%",
    left: "68%",
    width: "22%",
    height: "3%",
  },
  {
    field: "total_amount", 
    top: "75%",
    left: "75%",
    width: "15%",
    height: "4%",
  },
  {
    field: "due_date",
    top: "18.5%",
    left: "68%",
    width: "22%",
    height: "3%",
  },
]

interface DocumentViewerProps {
  documentId?: string
  activeField?: string
}

export function DocumentViewer({ documentId, activeField }: DocumentViewerProps) {
  const [imageError, setImageError] = useState(false)

  // Mock document image URL - in real app this would come from API
  const documentImageUrl = documentId 
    ? "https://i.imgur.com/7T20soH.png"
    : null

  if (!documentId) {
    return (
      <div className="flex-1 bg-gray-100 flex flex-col items-center justify-center p-4">
        <div className="text-center">
          <div className="w-24 h-24 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
            <svg
              className="w-12 h-12 text-gray-400"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              />
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            No Document Selected
          </h3>
          <p className="text-gray-500">
            Select a document from the list to view it here
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 bg-gray-100 flex flex-col items-center justify-center p-4 relative">
      <div className="w-full h-full bg-white rounded-lg shadow-inner border border-gray-200 overflow-auto relative">
        {documentImageUrl && !imageError ? (
          <>
            <img
              src={documentImageUrl}
              alt="Document Preview"
              className="w-full h-auto"
              onError={() => setImageError(true)}
            />
            
            {/* Highlight Boxes */}
            {mockHighlights.map((highlight) => (
              <div
                key={highlight.field}
                className={`
                  absolute bg-indigo-600/20 border-2 border-indigo-600 rounded z-10 transition-opacity
                  ${activeField === highlight.field ? "opacity-100" : "opacity-0"}
                `}
                style={{
                  top: highlight.top,
                  left: highlight.left,
                  width: highlight.width,
                  height: highlight.height,
                }}
              />
            ))}
          </>
        ) : (
          <div className="w-full h-full flex items-center justify-center">
            <div className="text-center">
              <div className="w-24 h-24 bg-gray-200 rounded-lg mx-auto mb-4 flex items-center justify-center">
                <svg
                  className="w-12 h-12 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Document Not Available
              </h3>
              <p className="text-gray-500">
                Unable to load the document preview
              </p>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
