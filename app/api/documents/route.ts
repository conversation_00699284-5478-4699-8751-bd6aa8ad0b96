import { NextRequest, NextResponse } from 'next/server';
import { requireDbUser } from '@/lib/auth';
import { documentOperations } from '@/lib/db';
import { getFileUrl } from '@/lib/s3';

export async function POST(request: NextRequest) {
  try {
    // Authenticate user and get database user
    const user = await requireDbUser();

    // Parse request body
    const body = await request.json();
    const { fileKey, filename, contentType, fileSize } = body;

    if (!fileKey || !filename || !contentType || !fileSize) {
      return NextResponse.json(
        {
          success: false,
          error:
            'Missing required fields: fileKey, filename, contentType, fileSize',
        },
        { status: 400 }
      );
    }

    // Generate the storage URL
    const storageUrl = getFileUrl(fileKey);

    // Create document record in database
    const document = await documentOperations.create({
      filename: fileKey,
      originalName: filename,
      mimeType: contentType,
      size: fileSize,
      storageUrl,
      userId: user.id,
    });

    return NextResponse.json({
      success: true,
      data: {
        documentId: document.id,
        document,
      },
    });
  } catch (error) {
    console.error('Error creating document record:', error);

    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
