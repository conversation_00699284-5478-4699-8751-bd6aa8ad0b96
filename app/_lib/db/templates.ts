import { prisma } from '../prisma';

// Template operations
export const templateOperations = {
  async findPublic() {
    return prisma.template.findMany({
      where: { isPublic: true },
      orderBy: { name: 'asc' },
    });
  },

  async findByUser(userId: string) {
    return prisma.template.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
    });
  },

  async create(input: {
    name: string;
    description?: string;
    documentType: string;
    fields: Record<string, unknown>;
    userId: string;
  }) {
    return prisma.template.create({
      data: {
        ...input,
        fields: input.fields as any, // Prisma expects JsonValue
      },
    });
  },
};
