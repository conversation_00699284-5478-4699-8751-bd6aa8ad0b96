import { prisma } from '../prisma';
import type { CreditTransactionType } from './types';

// Credit Transaction operations
export const creditOperations = {
  async consumeCredits(
    userId: string,
    amount: number,
    description: string,
    jobId?: string,
    documentId?: string
  ) {
    return prisma.$transaction(async (tx) => {
      // Check if user has enough credits
      const user = await tx.user.findUnique({
        where: { id: userId },
        select: { creditBalance: true },
      });

      if (!user || user.creditBalance < amount) {
        throw new Error('Insufficient credits');
      }

      // Deduct credits from user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          creditBalance: { decrement: amount },
          monthlyCreditsUsed: { increment: amount },
          totalCreditsUsed: { increment: amount },
        },
      });

      // Create credit transaction record
      return tx.creditTransaction.create({
        data: {
          userId,
          amount: -amount, // Negative for consumption
          type: 'CONSUMPTION',
          description,
          jobId,
          documentId,
        },
      });
    });
  },

  async addCredits(
    userId: string,
    amount: number,
    type: CreditTransactionType,
    description: string
  ) {
    return prisma.$transaction(async (tx) => {
      // Add credits to user balance
      await tx.user.update({
        where: { id: userId },
        data: {
          creditBalance: { increment: amount },
        },
      });

      // Create credit transaction record
      return tx.creditTransaction.create({
        data: {
          userId,
          amount, // Positive for addition
          type,
          description,
        },
      });
    });
  },

  async getCreditHistory(userId: string, limit = 50, offset = 0) {
    return prisma.creditTransaction.findMany({
      where: { userId },
      orderBy: { createdAt: 'desc' },
      take: limit,
      skip: offset,
      include: {
        job: {
          select: { id: true, type: true },
        },
        document: {
          select: { id: true, originalName: true },
        },
      },
    });
  },

  async resetMonthlyCredits(userId: string) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
      select: {
        monthlyCreditsLimit: true,
        creditBalance: true,
        monthlyCreditsUsed: true,
      },
    });

    if (!user) return null;

    // Calculate rollover credits (up to 2x monthly limit)
    const maxRollover = user.monthlyCreditsLimit * 2;
    const newBalance = Math.min(
      user.creditBalance + user.monthlyCreditsLimit,
      maxRollover
    );

    return prisma.$transaction(async (tx) => {
      // Reset monthly usage and update balance
      await tx.user.update({
        where: { id: userId },
        data: {
          monthlyCreditsUsed: 0,
          creditBalance: newBalance,
          lastCreditReset: new Date(),
        },
      });

      // Record the monthly credit allocation
      return tx.creditTransaction.create({
        data: {
          userId,
          amount: user.monthlyCreditsLimit,
          type: 'SUBSCRIPTION',
          description: 'Monthly credit allocation',
        },
      });
    });
  },
};
