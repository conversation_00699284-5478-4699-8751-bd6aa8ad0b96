'use client';

import * as React from 'react';
import Link from 'next/link';
import { LayoutDashboard, FileText, Puzzle, Code2 } from 'lucide-react';

import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarSeparator,
} from '@/components/ui/sidebar';
import { Logo } from './logo';
import { NavMain } from './nav-main';
import { NavCreditsUsage } from './nav-credits-usage';
import { NavUser } from './nav-user';

const data = {
  navMain: [
    {
      title: 'Dashboard',
      url: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      title: 'Documents',
      url: '/documents',
      icon: FileText,
    },
    {
      title: 'Templates',
      url: '/templates',
      icon: Puzzle,
    },
    {
      title: 'API',
      url: '/api-keys',
      icon: Code2,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="offcanvas" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              asChild
              className="data-[slot=sidebar-menu-button]:!p-1.5"
            >
              <Link href="/">
                <Logo className="size-5" />
                <span className="text-base font-semibold">Unfurl</span>
              </Link>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>

      <SidebarContent>
        <NavMain items={data.navMain} />
      </SidebarContent>

      <SidebarFooter className="gap-4">
        <NavCreditsUsage />
        <SidebarSeparator className="mx-0" />
        <NavUser />
      </SidebarFooter>
    </Sidebar>
  );
}
