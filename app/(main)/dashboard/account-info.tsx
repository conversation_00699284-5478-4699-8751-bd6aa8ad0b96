'use client';

import { useUser } from '@/contexts/user-context';
import { Skeleton } from '@/components/ui/skeleton';

export function AccountInfo() {
  const { dbUser } = useUser();

  return (
    <div className="bg-white rounded-lg shadow p-6 mb-8">
      <h2 className="text-xl font-semibold mb-4">Account Information</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div>
          <p className="text-sm text-gray-600">Email</p>
          {dbUser ? (
            <p className="font-medium">{dbUser.email}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Credits Available</p>
          {dbUser ? (
            <p className="font-medium text-green-600">{dbUser.creditBalance}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Subscription Plan</p>
          {dbUser ? (
            <p className="font-medium">{dbUser.subscriptionPlan}</p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
        <div>
          <p className="text-sm text-gray-600">Member Since</p>
          {dbUser ? (
            <p className="font-medium">
              {new Date(dbUser.createdAt).toLocaleDateString()}
            </p>
          ) : (
            <Skeleton className="h-6" />
          )}
        </div>
      </div>
    </div>
  );
}
