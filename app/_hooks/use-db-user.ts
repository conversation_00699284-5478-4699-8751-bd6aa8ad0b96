'use client';

import { useState, useEffect } from 'react';
import { useUser } from '@clerk/nextjs';
import type { User } from '@prisma/client';

interface UseDbUserReturn {
  dbUser: User | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

/**
 * Custom hook to fetch and manage database user data in client components
 * This hook automatically fetches the user data when the Clerk user is available
 * and provides loading states and error handling
 */
export function useDbUser(): UseDbUserReturn {
  const { user: clerkUser, isLoaded } = useUser();
  const [dbUser, setDbUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDbUser = async () => {
    if (!clerkUser) {
      setDbUser(null);
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/user/me');

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error('User not found in database');
        }
        throw new Error('Failed to fetch user data');
      }

      const userData = await response.json();
      setDbUser(userData);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      console.error('Error fetching database user:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (isLoaded) {
      fetchDbUser();
    }
  }, [clerkUser?.id, isLoaded]);

  return {
    dbUser,
    loading: loading || !isLoaded,
    error,
    refetch: fetchDbUser,
  };
}
