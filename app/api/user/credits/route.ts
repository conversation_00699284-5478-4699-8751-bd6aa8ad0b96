import { NextRequest, NextResponse } from 'next/server';
import { requireAuth } from '@/lib/auth';
import { userOperations } from '@/lib/db';

export async function GET(req: NextRequest) {
  try {
    const userId = await requireAuth();
    const creditBalance = await userOperations.checkCreditBalance(userId);
    
    if (!creditBalance) {
      return NextResponse.json(
        { success: false, error: 'User not found' },
        { status: 404 }
      );
    }
    
    return NextResponse.json({
      success: true,
      data: creditBalance,
    });
  } catch (error) {
    console.error('Error fetching credit balance:', error);
    
    if (error instanceof Error && error.message === 'Unauthorized') {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }
    
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
