# **Unfurl \- Comprehensive Task List (Next.js)**

This document provides a detailed, granular breakdown of all tasks required to build the Unfurl application. It is designed to be used by an AI coding assistant to ensure each step is understood and executed correctly.

### **Sprint 1: Core Application Setup & Authentication**

**Goal:** Establish the foundational codebase, connect it to a database, and implement a complete, secure user authentication system.

* **Task ID: S1-01 \- Initialize Next.js Project**
  * **Sub-task 1.1.1:** Run npx create-next-app@latest to initialize a new project.
    * **Acceptance Criteria:** The project is created with **TypeScript**, **ESLint**, **Tailwind CSS**, and the **App Router**.
  * **Sub-task 1.1.2:** Create the initial directory structure inside the /app folder.
    * **Acceptance Criteria:** Create folders: \_components, \_lib, (auth), (main). This structure separates UI components, utility functions, and route groups for authenticated and unauthenticated users.
* **Task ID: S1-02 \- Set Up Database with Prisma**
  * **Sub-task 1.2.1:** Install Prisma (npm install prisma \--save-dev) and initialize it (npx prisma init).
    * **Acceptance Criteria:** A prisma directory with a schema.prisma file is created. The .env file is populated with the DATABASE\_URL.
  * **Sub-task 1.2.2:** Define all necessary models in schema.prisma.
    * **Acceptance Criteria:** The schema includes a User model with a clerkId (string, unique) to link to Clerk users, and a credits field. It also includes a Job model for tracking document processing.
  * **Sub-task 1.2.3:** Run the initial database migration.
    * **Acceptance Criteria:** Execute npx prisma migrate dev \--name init. The database tables are successfully created and match the Prisma schema.
* **Task ID: S1-03 \- Implement Authentication with Clerk**
  * **Sub-task 1.3.1:** Install the Clerk Next.js SDK (npm install @clerk/nextjs).
  * **Sub-task 1.3.2:** Configure Clerk environment variables in .env.local (NEXT\_PUBLIC\_CLERK\_PUBLISHABLE\_KEY, CLERK\_SECRET\_KEY).
  * **Sub-task 1.3.3:** Wrap the root layout (/app/layout.tsx) with the \<ClerkProvider\>.
    * **Acceptance Criteria:** This makes Clerk's authentication state available throughout the application.
  * **Sub-task 1.3.4:** Create the authentication pages using Clerk's pre-built components.
    * **Acceptance Criteria:** Create /sign-in/\[\[...sign-in\]\]/page.tsx and /sign-up/\[\[...sign-up\]\]/page.tsx routes. Use the \<SignIn /\> and \<SignUp /\> components to render the UI. Clerk will handle the entire login/registration flow.
  * **Sub-task 1.3.5:** Set up a webhook handler to sync Clerk users with the local database.
    * **Acceptance Criteria:** Create an API route at /api/webhooks/clerk. This route will listen for user.created events from Clerk and create a corresponding User record in the local PostgreSQL database via Prisma.
* **Task ID: S1-04 \- Create Core UI Layout & Session Management**
  * **Sub-task 1.4.1:** Build the main application layout in (main)/layout.tsx, including the responsive sidebar and top navigation bar from the mockup.
  * **Sub-task 1.4.2:** Protect routes using Clerk's middleware.
    * **Acceptance Criteria:** Create a middleware.ts file that protects all routes by default, except for public pages like the landing and pricing pages.
  * **Sub-task 1.4.3:** Display user-specific UI.
    * **Acceptance Criteria:** Use Clerk's \<UserButton /\> component in the top navigation bar for a ready-made profile/sign-out menu. Fetch the current user's ID with the auth() helper in server components to display user-specific data.

### **Sprint 2: Document Processing & Validation Workflow**

**Goal:** Implement the application's core feature: allowing users to upload a document, have it processed by an AI, and view the results.

* **Task ID: S2-01 \- Implement Secure File Uploads** ✅ **COMPLETED**
  * **Sub-task 2.1.1:** Create a server-side API route at /api/upload to handle file uploads. ✅
    * **Acceptance Criteria:** This route must authenticate the user, check if their credit balance is greater than zero, and then generate a secure, presigned URL for uploading directly to a cloud storage provider (e.g., AWS S3).
  * **Sub-task 2.1.2:** Build the interactive upload modal on the frontend. ✅
    * **Acceptance Criteria:** When a file is selected, the frontend first calls the /api/upload route to get the presigned URL, then uploads the file directly to cloud storage using that URL.
* **Task ID: S2-02 \- Implement Asynchronous Job Processing**
  * **Sub-task 2.2.1:** Upon a successful file upload, create a new Job record in the database with a status of PENDING.
  * **Sub-task 2.2.2:** Integrate a serverless queue (e.g., Upstash).
    * **Acceptance Criteria:** After creating the Job record, a message containing the jobId is published to the queue.
* **Task ID: S2-03 \- Integrate AI Service via Serverless Function**
  * **Sub-task 2.3.1:** Create a new API route (e.g., /api/process) to act as the consumer for the job queue.
    * **Acceptance Criteria:** This endpoint should be protected and only callable by the queueing service (e.g., using a secret key).
  * **Sub-task 2.3.2:** Implement the AI processing logic within this route.
    * **Acceptance Criteria:** The function should:
      1. Fetch the job details from the database using the jobId.
      2. Call the external AI service (e.g., Google Vision API) with the document's URL.
      3. **Decrement the user's credit balance** by the number of pages processed.
      4. Save the structured JSON result from the AI back to the Job record.
      5. Update the Job status to COMPLETED or FAILED.
* **Task ID: S2-04 \- Build the Document Validation UI**
  * **Sub-task 2.4.1:** Create the dynamic page (main)/documents/\[documentId\]/page.tsx.
    * **Acceptance Criteria:** This page fetches the job data for the given documentId from the database.
  * **Sub-task 2.4.2:** Implement the three-panel layout from the interactive mockup.
    * **Acceptance Criteria:** The left panel lists all of the user's jobs. The center panel displays the document image. The right panel displays an editable form pre-filled with the extracted data from the Job record.

### **Sprint 3: Monetization & Public-Facing Features**

**Goal:** Implement the business logic for payments and expose the service for developer use via an API.

* **Task ID: S3-01 \- Build Marketing & Pricing Pages**
  * **Sub-task 3.1.1:** Create static pages for the landing page (/) and pricing page (/pricing) using the mockups.
    * **Acceptance Criteria:** These pages are server-rendered for SEO and performance.
* **Task ID: S3-02 \- Integrate Stripe for Subscriptions**
  * **Sub-task 3.2.1:** Create API routes for Stripe integration.
    * **Acceptance Criteria:** An API route /api/stripe/checkout creates a Stripe Checkout session. A separate webhook route /api/stripe/webhook listens for events from Stripe.
  * **Sub-task 3.2.2:** Implement the webhook logic.
    * **Acceptance Criteria:** The webhook must securely verify the signature from Stripe. On a checkout.session.completed event, it must update the user's credits and plan in the database.
* **Task ID: S3-03 \- Implement Feature Gating**
  * **Sub-task 3.3.1:** Create a server-side utility function to check a user's plan and permissions.
  * **Sub-task 3.3.2:** Implement feature locks in the UI and API.
    * **Acceptance Criteria:** The "Data Export" button is disabled in the UI for Free plan users. The /api/documents/{jobId}/data endpoint returns a 403 Forbidden error if a Free plan user tries to access it.
* **Task ID: S3-04 \- Build Developer API**
  * **Sub-task 3.4.1:** Implement API key generation and management on a user settings page.
    * **Acceptance Criteria:** Users can generate, view, and revoke API keys. Keys are hashed before being stored in the database.
  * **Sub-task 3.4.2:** Create a middleware or wrapper function to protect API routes.
    * **Acceptance Criteria:** The middleware checks for a valid Authorization: Bearer \<API\_KEY\> header and authenticates the user based on the key.

### **Sprint 4: Pre-Launch Polish & Deployment**

**Goal:** Harden the application, ensure its reliability through testing, and prepare it for production deployment.

* **Task ID: S4-01 \- Comprehensive Testing**
  * **Sub-task 4.1.1:** Set up Jest and React Testing Library.
  * **Sub-task 4.1.2:** Write unit tests for critical utility functions and components.
  * **Sub-task 4.1.3:** Write integration tests for the full authentication flow and the document upload-to-validation workflow.
* **Task ID: S4-02 \- Set Up CI/CD Pipeline**
  * **Sub-task 4.2.1:** Configure a main and develop branch strategy in Git.
  * **Sub-task 4.2.2:** Create a GitHub Actions workflow.
    * **Acceptance Criteria:** The workflow automatically runs ESLint, Prettier, and all tests on every push to the develop branch. Pushes to main trigger a production deployment to Vercel.
* **Task ID: S4-03 \- Documentation**
  * **Sub-task 4.3.1:** Write end-user guides for the help section of the application.
  * **Sub-task 4.3.2:** Create a public-facing developer documentation site for the API using a tool like Mintlify or Nextra.
* **Task ID: S4-04 \- Beta Testing & Final Polish**
  * **Sub-task 4.4.1:** Deploy the application to a staging environment for internal testing.
  * **Sub-task 4.4.2:** Onboard a small group of beta testers.
    * **Acceptance Criteria:** Collect and triage feedback. Create new tasks in the backlog for any bugs or critical usability issues discovered.